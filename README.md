# Picky Store - Next.js E-commerce Client

A modern, high-performance e-commerce application built with Next.js 15, TypeScript, and best practices inspired by enterprise-level applications.

## 🚀 Features

### Core Features
- **Modern Architecture**: Next.js 15 with App Router for optimal performance
- **Type Safety**: Full TypeScript implementation with strict typing
- **State Management**: Zustand for client state, TanStack Query for server state
- **Authentication**: Cookie-based authentication with automatic token refresh
- **Responsive Design**: Mobile-first approach with clean, minimalist UI
- **SEO Optimized**: Server-side rendering with comprehensive meta tags

### E-commerce Features
- Product browsing with advanced filtering
- Shopping cart with persistence
- User authentication and account management
- Wishlist functionality
- Order management
- Payment integration (Stripe, PayPal)
- Product search and recommendations

### Technical Features
- **Performance**: Optimized for Core Web Vitals
- **Accessibility**: WCAG 2.1 AA compliance
- **Security**: HTTP-only cookies, CSRF protection, input validation
- **Caching**: Intelligent caching strategies with TanStack Query
- **Error Handling**: Comprehensive error boundaries and user feedback

## 🛠 Tech Stack

### Frontend
- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: SCSS Modules with custom design system
- **State Management**: Zustand + TanStack Query v5
- **Forms**: React Hook Form + Zod validation
- **HTTP Client**: Axios with interceptors
- **Icons**: React Icons
- **Date Handling**: Luxon

### Development
- **Package Manager**: npm
- **Code Quality**: ESLint + TypeScript strict mode
- **Development Tools**: TanStack Query Devtools

## 📦 Installation

### Prerequisites
- Node.js 18+
- npm or yarn
- Backend API server running

### Setup

1. **Install dependencies**
   ```bash
   npm install zustand@^5.0.6 @tanstack/react-query@^5.35.1 axios@^1.6.8 react-hook-form@^7.51.5 @hookform/resolvers@^3.4.2 zod@^3.23.8 sass@^1.76.0 react-icons@^5.2.0 react-spinners@^0.13.8 luxon@^3.4.4 lodash@^4.17.21 dompurify@^3.1.7 react-phone-input-2@^2.15.1
   ```

2. **Install payment dependencies**
   ```bash
   npm install @stripe/react-stripe-js@^2.7.3 @stripe/stripe-js@^4.0.0 @paypal/react-paypal-js@^8.3.0
   ```

3. **Install dev dependencies**
   ```bash
   npm install --save-dev @types/lodash@^4.17.0 @types/dompurify@^3.0.5 @types/luxon@^3.4.2 @tanstack/react-query-devtools@^5.35.1 @hookform/devtools@^4.3.1
   ```

4. **Environment setup**
   ```bash
   cp .env.example .env.local
   ```

   Update the environment variables:
   ```env
   NEXT_PUBLIC_API_BASE_URL=http://127.0.0.1:8000
   NEXT_PUBLIC_SITE_URL=http://localhost:3000
   NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
   NEXT_PUBLIC_PAYPAL_CLIENT_ID=your_paypal_client_id
   ```

5. **Run the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)
