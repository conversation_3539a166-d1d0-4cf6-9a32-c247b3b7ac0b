// Auth layout for login, register, and related pages
// Provides a centered layout with auth-specific styling

import Link from 'next/link';
import styles from './layout.module.scss';

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className={styles.authLayout}>
      <div className={styles.container}>
        <div className={styles.header}>
          <Link href="/" className={styles.logo}>
            Picky Store
          </Link>
        </div>
        
        <div className={styles.content}>
          {children}
        </div>
        
        <div className={styles.footer}>
          <p className={styles.footerText}>
            © 2024 Picky Store. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  );
}
