'use client';

// Register page with form validation and account creation
// Handles user registration and redirects on success

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { FiEye, FiEyeOff, FiMail, FiLock, FiUser } from 'react-icons/fi';
import { Button } from '../../../src/components/ui/Button';
import { Input } from '../../../src/components/ui/Input';
import { AuthService } from '../../../src/services/api-client';
import { useAuthStore } from '../../../src/stores/auth-store';
import { useUIActions } from '../../../src/stores/ui-store';
import styles from './page.module.scss';

const registerSchema = z.object({
  first_name: z.string().min(1, 'First name is required'),
  last_name: z.string().min(1, 'Last name is required'),
  email: z.string().email('Please enter a valid email address'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one uppercase letter, one lowercase letter, and one number'),
  confirm_password: z.string(),
}).refine((data) => data.password === data.confirm_password, {
  message: "Passwords don't match",
  path: ["confirm_password"],
});

type RegisterFormData = z.infer<typeof registerSchema>;

export default function RegisterPage() {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { setUser, setAuthenticated } = useAuthStore();
  const { addNotification } = useUIActions();

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
  });

  const onSubmit = async (data: RegisterFormData) => {
    setIsLoading(true);
    
    try {
      const response = await AuthService.register(data);
      setUser(response.user);
      setAuthenticated(true);
      
      addNotification({
        type: 'success',
        title: 'Welcome to Picky Store!',
        message: 'Your account has been created successfully.',
      });
      
      // Redirect to home or intended page
      router.push('/');
    } catch (error: any) {
      console.error('Registration error:', error);
      
      if (error.errors) {
        // Handle field-specific errors
        Object.entries(error.errors).forEach(([field, messages]) => {
          if (Array.isArray(messages) && messages.length > 0) {
            setError(field as keyof RegisterFormData, {
              message: messages[0],
            });
          }
        });
      } else {
        addNotification({
          type: 'error',
          title: 'Registration failed',
          message: error.message || 'An unexpected error occurred. Please try again.',
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={styles.registerPage}>
      <div className={styles.header}>
        <h1 className={styles.title}>Create Account</h1>
        <p className={styles.subtitle}>Join Picky Store and start shopping</p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className={styles.form}>
        <div className={styles.nameFields}>
          <Input
            {...register('first_name')}
            type="text"
            label="First Name"
            placeholder="Enter your first name"
            error={errors.first_name?.message}
            leftIcon={<FiUser size={18} />}
            fullWidth
          />
          
          <Input
            {...register('last_name')}
            type="text"
            label="Last Name"
            placeholder="Enter your last name"
            error={errors.last_name?.message}
            leftIcon={<FiUser size={18} />}
            fullWidth
          />
        </div>

        <Input
          {...register('email')}
          type="email"
          label="Email Address"
          placeholder="Enter your email"
          error={errors.email?.message}
          leftIcon={<FiMail size={18} />}
          fullWidth
        />

        <Input
          {...register('password')}
          type={showPassword ? 'text' : 'password'}
          label="Password"
          placeholder="Create a password"
          error={errors.password?.message}
          helperText="Must be at least 8 characters with uppercase, lowercase, and number"
          leftIcon={<FiLock size={18} />}
          rightIcon={
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className={styles.passwordToggle}
            >
              {showPassword ? <FiEyeOff size={18} /> : <FiEye size={18} />}
            </button>
          }
          fullWidth
        />

        <Input
          {...register('confirm_password')}
          type={showConfirmPassword ? 'text' : 'password'}
          label="Confirm Password"
          placeholder="Confirm your password"
          error={errors.confirm_password?.message}
          leftIcon={<FiLock size={18} />}
          rightIcon={
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className={styles.passwordToggle}
            >
              {showConfirmPassword ? <FiEyeOff size={18} /> : <FiEye size={18} />}
            </button>
          }
          fullWidth
        />

        <Button
          type="submit"
          variant="primary"
          size="lg"
          loading={isLoading}
          fullWidth
        >
          Create Account
        </Button>
      </form>

      <div className={styles.footer}>
        <p className={styles.loginPrompt}>
          Already have an account?{' '}
          <Link href="/auth/login" className={styles.loginLink}>
            Sign in here
          </Link>
        </p>
      </div>
    </div>
  );
}
