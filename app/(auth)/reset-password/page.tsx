'use client'

// Password reset confirmation page
// Handles new password submission with token validation

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter, useSearchParams } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { FiLock, FiEye, FiEyeOff, FiArrowLeft } from 'react-icons/fi'
import { Button } from '../../../src/components/ui/Button'
import { Input } from '../../../src/components/ui/Input'
import { useResetPassword } from '../../../src/hooks/queries/useAuth'
import styles from './page.module.scss'

const resetPasswordSchema = z.object({
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 
      'Password must contain at least one uppercase letter, one lowercase letter, and one number'),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>

export default function ResetPasswordPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)
  const [token, setToken] = useState<string | null>(null)

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordSchema),
  })

  const resetMutation = useResetPassword()

  useEffect(() => {
    const tokenParam = searchParams.get('token')
    if (!tokenParam) {
      router.push('/auth/forgot-password')
      return
    }
    setToken(tokenParam)
  }, [searchParams, router])

  const onSubmit = async (data: ResetPasswordFormData) => {
    if (!token) {
      setError('root', { message: 'Invalid reset token' })
      return
    }

    try {
      await resetMutation.mutateAsync({
        token,
        password: data.password,
      })
      setIsSuccess(true)
    } catch (error: any) {
      if (error.errors) {
        Object.entries(error.errors).forEach(([field, messages]) => {
          if (Array.isArray(messages) && messages.length > 0) {
            setError(field as keyof ResetPasswordFormData, {
              message: messages[0],
            })
          }
        })
      } else {
        setError('root', {
          message: error.message || 'Failed to reset password. Please try again.',
        })
      }
    }
  }

  if (isSuccess) {
    return (
      <div className={styles.resetPasswordPage}>
        <div className={styles.successContainer}>
          <div className={styles.successIcon}>✅</div>
          <h1 className={styles.title}>Password Reset Successful!</h1>
          <p className={styles.subtitle}>
            Your password has been successfully reset. You can now log in with your new password.
          </p>
          
          <div className={styles.actions}>
            <Button
              variant="primary"
              size="lg"
              onClick={() => router.push('/auth/login')}
              fullWidth
            >
              Go to Login
            </Button>
          </div>
        </div>
      </div>
    )
  }

  if (!token) {
    return (
      <div className={styles.resetPasswordPage}>
        <div className={styles.errorContainer}>
          <div className={styles.errorIcon}>❌</div>
          <h1 className={styles.title}>Invalid Reset Link</h1>
          <p className={styles.subtitle}>
            This password reset link is invalid or has expired.
          </p>
          
          <div className={styles.actions}>
            <Button
              variant="primary"
              size="lg"
              onClick={() => router.push('/auth/forgot-password')}
              fullWidth
            >
              Request New Reset Link
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.resetPasswordPage}>
      <div className={styles.header}>
        <h1 className={styles.title}>Reset Your Password</h1>
        <p className={styles.subtitle}>
          Enter your new password below. Make sure it's strong and secure.
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className={styles.form}>
        <Input
          {...register('password')}
          type={showPassword ? 'text' : 'password'}
          label="New Password"
          placeholder="Enter your new password"
          error={errors.password?.message}
          leftIcon={<FiLock size={18} />}
          rightIcon={
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className={styles.passwordToggle}
            >
              {showPassword ? <FiEyeOff size={18} /> : <FiEye size={18} />}
            </button>
          }
          fullWidth
        />

        <Input
          {...register('confirmPassword')}
          type={showConfirmPassword ? 'text' : 'password'}
          label="Confirm New Password"
          placeholder="Confirm your new password"
          error={errors.confirmPassword?.message}
          leftIcon={<FiLock size={18} />}
          rightIcon={
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className={styles.passwordToggle}
            >
              {showConfirmPassword ? <FiEyeOff size={18} /> : <FiEye size={18} />}
            </button>
          }
          fullWidth
        />

        {errors.root && (
          <div className={styles.errorMessage}>
            {errors.root.message}
          </div>
        )}

        <Button
          type="submit"
          variant="primary"
          size="lg"
          loading={resetMutation.isPending}
          fullWidth
        >
          Reset Password
        </Button>
      </form>

      <div className={styles.footer}>
        <Link href="/auth/login" className={styles.backLink}>
          <FiArrowLeft size={16} />
          Back to Login
        </Link>
      </div>
    </div>
  )
}
