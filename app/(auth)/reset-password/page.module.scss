@use '../../../src/styles/variables' as *;
@use '../../../src/styles/mixins' as *;

.resetPasswordPage {
  @include flexbox(center, center);
  min-height: 100vh;
  padding: $spacing-4;
  background: linear-gradient(135deg, $gray-50 0%, $gray-100 100%);
}

.header {
  text-align: center;
  margin-bottom: $spacing-8;
}

.title {
  font-size: clamp(1.75rem, 4vw, 2.25rem);
  font-weight: 700;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-3;
  line-height: 1.2;
}

.subtitle {
  font-size: $font-size-2;
  color: $primary-lighter-text-color;
  line-height: 1.5;
  max-width: 400px;
  margin: 0 auto;
}

.form {
  display: flex;
  flex-direction: column;
  gap: $spacing-4;
  width: 100%;
  max-width: 400px;
  margin-bottom: $spacing-6;
}

.passwordToggle {
  background: none;
  border: none;
  color: $primary-lighter-text-color;
  cursor: pointer;
  padding: 0;
  @include flexbox(center, center);
  transition: color $transition-fast;

  &:hover {
    color: $primary-blue;
  }
}

.errorMessage {
  background: $error-bg;
  color: $error-text;
  padding: $spacing-3 $spacing-4;
  border-radius: $border-radius-md;
  font-size: $font-size-1;
  text-align: center;
  border: 1px solid $error;
}

.footer {
  text-align: center;
}

.backLink {
  @include flexbox(center, center);
  gap: $spacing-2;
  color: $primary-blue;
  text-decoration: none;
  font-size: $font-size-2;
  font-weight: 500;
  transition: color $transition-fast;

  &:hover {
    color: $primary-blue-dark;
  }
}

// Success state styles
.successContainer,
.errorContainer {
  text-align: center;
  max-width: 500px;
  width: 100%;
}

.successIcon,
.errorIcon {
  font-size: 4rem;
  margin-bottom: $spacing-6;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: $spacing-4;
  align-items: center;
}

// Password strength indicator
.passwordStrength {
  margin-top: $spacing-2;
  padding: $spacing-2;
  border-radius: $border-radius-sm;
  font-size: $font-size-1;
  
  &.weak {
    background: $error-bg;
    color: $error-text;
  }
  
  &.medium {
    background: $warning-bg;
    color: $warning-text;
  }
  
  &.strong {
    background: $success-bg;
    color: $success-text;
  }
}

// Responsive design
@include mobile-only {
  .resetPasswordPage {
    padding: $spacing-2;
  }

  .title {
    font-size: 1.5rem;
  }

  .subtitle {
    font-size: $font-size-1;
  }

  .form {
    max-width: 100%;
  }
}
