'use client'

// <PERSON>gin page with form validation and authentication
// <PERSON>les user login and redirects on success

import { useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { FiEye, FiEyeOff, FiMail, FiLock } from 'react-icons/fi'
import { Button } from '../../../src/components/ui/Button'
import { Input } from '../../../src/components/ui/Input'
import { AuthService } from '../../../src/services/api-client'
import { useAuthStore } from '../../../src/stores/auth-store'
import { useUIActions } from '../../../src/stores/ui-store'
import styles from './page.module.scss'

const loginSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(1, 'Password is required'),
})

type LoginFormData = z.infer<typeof loginSchema>

export default function LoginPage() {
  const router = useRouter()
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const { setUser, setAuthenticated } = useAuthStore()
  const { addNotification } = useUIActions()

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  })

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true)

    try {
      const response = await AuthService.login(data)
      setUser(response.user)
      setAuthenticated(true)

      addNotification({
        type: 'success',
        title: 'Welcome back!',
        message: 'You have been successfully logged in.',
      })

      // Redirect to home or intended page
      router.push('/')
    } catch (error: any) {
      console.error('Login error:', error)

      if (error.status === 401) {
        setError('root', {
          message: 'Invalid email or password. Please try again.',
        })
      } else if (error.errors) {
        // Handle field-specific errors
        Object.entries(error.errors).forEach(([field, messages]) => {
          if (Array.isArray(messages) && messages.length > 0) {
            setError(field as keyof LoginFormData, {
              message: messages[0],
            })
          }
        })
      } else {
        addNotification({
          type: 'error',
          title: 'Login failed',
          message: error.message || 'An unexpected error occurred. Please try again.',
        })
      }
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className={styles.loginPage}>
      <div className={styles.header}>
        <h1 className={styles.title}>Welcome Back</h1>
        <p className={styles.subtitle}>Sign in to your account to continue</p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className={styles.form}>
        <Input
          {...register('email')}
          type="email"
          label="Email Address"
          placeholder="Enter your email"
          error={errors.email?.message}
          leftIcon={<FiMail size={18} />}
          fullWidth
        />

        <Input
          {...register('password')}
          type={showPassword ? 'text' : 'password'}
          label="Password"
          placeholder="Enter your password"
          error={errors.password?.message}
          leftIcon={<FiLock size={18} />}
          rightIcon={
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className={styles.passwordToggle}
            >
              {showPassword ? <FiEyeOff size={18} /> : <FiEye size={18} />}
            </button>
          }
          fullWidth
        />

        {errors.root && (
          <div className={styles.errorMessage}>
            {errors.root.message}
          </div>
        )}

        <div className={styles.forgotPassword}>
          <Link href="/auth/forgot-password" className={styles.forgotLink}>
            Forgot your password?
          </Link>
        </div>

        <Button
          type="submit"
          variant="primary"
          size="lg"
          loading={isLoading}
          fullWidth
        >
          Sign In
        </Button>
      </form>

      <div className={styles.footer}>
        <p className={styles.signupPrompt}>
          Don&#39;t have an account?{' '}
          <Link href="/auth/register" className={styles.signupLink}>
            Sign up here
          </Link>
        </p>
      </div>
    </div>
  )
}
