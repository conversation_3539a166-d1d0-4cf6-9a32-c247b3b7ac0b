@use '../../../src/styles/variables' as *;
@use '../../../src/styles/mixins' as *;

.forgotPasswordPage {
  @include flexbox(center, center);
  min-height: 100vh;
  padding: $spacing-4;
  background: linear-gradient(135deg, $gray-50 0%, $gray-100 100%);
}

.header {
  text-align: center;
  margin-bottom: $spacing-8;
}

.title {
  font-size: clamp(1.75rem, 4vw, 2.25rem);
  font-weight: 700;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-3;
  line-height: 1.2;
}

.subtitle {
  font-size: $font-size-2;
  color: $primary-lighter-text-color;
  line-height: 1.5;
  max-width: 400px;
  margin: 0 auto;
}

.form {
  display: flex;
  flex-direction: column;
  gap: $spacing-4;
  width: 100%;
  max-width: 400px;
  margin-bottom: $spacing-6;
}

.errorMessage {
  background: $error-bg;
  color: $error-text;
  padding: $spacing-3 $spacing-4;
  border-radius: $border-radius-md;
  font-size: $font-size-1;
  text-align: center;
  border: 1px solid $error;
}

.footer {
  text-align: center;
}

.backLink {
  @include flexbox(center, center);
  gap: $spacing-2;
  color: $primary-blue;
  text-decoration: none;
  font-size: $font-size-2;
  font-weight: 500;
  transition: color $transition-fast;

  &:hover {
    color: $primary-blue-dark;
  }
}

// Success state styles
.successContainer {
  text-align: center;
  max-width: 500px;
  width: 100%;
}

.successIcon {
  font-size: 4rem;
  margin-bottom: $spacing-6;
}

.email {
  font-size: $font-size-3;
  font-weight: 600;
  color: $primary-blue;
  margin: $spacing-4 0;
  padding: $spacing-3 $spacing-4;
  background: $sky-lighter-blue;
  border-radius: $border-radius-md;
  border: 1px solid $primary-blue;
}

.instructions {
  font-size: $font-size-2;
  color: $primary-lighter-text-color;
  line-height: 1.6;
  margin-bottom: $spacing-8;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: $spacing-4;
  align-items: center;
}

// Responsive design
@include mobile-only {
  .forgotPasswordPage {
    padding: $spacing-2;
  }

  .title {
    font-size: 1.5rem;
  }

  .subtitle {
    font-size: $font-size-1;
  }

  .form {
    max-width: 100%;
  }
}
