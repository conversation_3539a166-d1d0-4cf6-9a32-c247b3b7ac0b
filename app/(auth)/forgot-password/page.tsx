'use client'

// Forgot password page - request password reset
// <PERSON>les email submission for password reset

import { useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { FiMail, FiArrowLeft } from 'react-icons/fi'
import { Button } from '../../../src/components/ui/Button'
import { Input } from '../../../src/components/ui/Input'
import { useRequestPasswordReset } from '../../../src/hooks/queries/useAuth'
import styles from './page.module.scss'

const forgotPasswordSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
})

type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>

export default function ForgotPasswordPage() {
  const router = useRouter()
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [submittedEmail, setSubmittedEmail] = useState('')

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
  })

  const resetMutation = useRequestPasswordReset()

  const onSubmit = async (data: ForgotPasswordFormData) => {
    try {
      await resetMutation.mutateAsync(data.email)
      setSubmittedEmail(data.email)
      setIsSubmitted(true)
    } catch (error: any) {
      if (error.errors) {
        Object.entries(error.errors).forEach(([field, messages]) => {
          if (Array.isArray(messages) && messages.length > 0) {
            setError(field as keyof ForgotPasswordFormData, {
              message: messages[0],
            })
          }
        })
      } else {
        setError('root', {
          message: error.message || 'Failed to send reset email. Please try again.',
        })
      }
    }
  }

  if (isSubmitted) {
    return (
      <div className={styles.forgotPasswordPage}>
        <div className={styles.successContainer}>
          <div className={styles.successIcon}>📧</div>
          <h1 className={styles.title}>Check Your Email</h1>
          <p className={styles.subtitle}>
            We've sent password reset instructions to:
          </p>
          <p className={styles.email}>{submittedEmail}</p>
          <p className={styles.instructions}>
            Please check your email and follow the instructions to reset your password.
            If you don't see the email, check your spam folder.
          </p>
          
          <div className={styles.actions}>
            <Button
              variant="primary"
              onClick={() => setIsSubmitted(false)}
              fullWidth
            >
              Try Different Email
            </Button>
            <Link href="/auth/login" className={styles.backLink}>
              <FiArrowLeft size={16} />
              Back to Login
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.forgotPasswordPage}>
      <div className={styles.header}>
        <h1 className={styles.title}>Forgot Password?</h1>
        <p className={styles.subtitle}>
          Enter your email address and we'll send you instructions to reset your password.
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className={styles.form}>
        <Input
          {...register('email')}
          type="email"
          label="Email Address"
          placeholder="Enter your email address"
          error={errors.email?.message}
          leftIcon={<FiMail size={18} />}
          fullWidth
        />

        {errors.root && (
          <div className={styles.errorMessage}>
            {errors.root.message}
          </div>
        )}

        <Button
          type="submit"
          variant="primary"
          size="lg"
          loading={resetMutation.isPending}
          fullWidth
        >
          Send Reset Instructions
        </Button>
      </form>

      <div className={styles.footer}>
        <Link href="/auth/login" className={styles.backLink}>
          <FiArrowLeft size={16} />
          Back to Login
        </Link>
      </div>
    </div>
  )
}
