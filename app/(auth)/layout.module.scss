.authLayout {
  min-height: 100vh;
  background: linear-gradient(135deg, $primary-blue 0%, $primary-blue-dark 100%);
  @include flexbox(center, center);
  padding: $spacing-4;
}

.container {
  background: white;
  border-radius: $border-radius-xl;
  box-shadow: $box-shadow-lg;
  width: 100%;
  max-width: 450px;
  overflow: hidden;
}

.header {
  background: $gray-50;
  padding: $spacing-6;
  text-align: center;
  border-bottom: 1px solid $gray-200;
}

.logo {
  font-size: $font-size-5;
  font-weight: 700;
  color: $primary-blue;
  text-decoration: none;
}

.content {
  padding: $spacing-8;
}

.footer {
  background: $gray-50;
  padding: $spacing-4;
  text-align: center;
  border-top: 1px solid $gray-200;
}

.footerText {
  font-size: $font-size-1;
  color: $gray-600;
  margin: 0;
}
