'use client'

// Profile edit page
// Allows users to update their personal information

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { FiUser, FiMail, FiPhone, FiCalendar, FiSave, FiArrowLeft } from 'react-icons/fi'
import { MainLayout } from '../../../../src/components/layout/MainLayout'
import { Button } from '../../../../src/components/ui/Button'
import { Input } from '../../../../src/components/ui/Input'
import { useAuthStore, useAuthActions } from '../../../../src/stores/auth-store'
import { useUIActions } from '../../../../src/stores/ui-store'
import styles from './page.module.scss'

const profileSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().optional(),
  dateOfBirth: z.string().optional(),
  bio: z.string().max(500, 'Bio must be less than 500 characters').optional(),
})

type ProfileFormData = z.infer<typeof profileSchema>

export default function ProfilePage() {
  const router = useRouter()
  const { user, isAuthenticated } = useAuthStore()
  const { updateProfile } = useAuthActions()
  const { addNotification } = useUIActions()
  const [isUpdating, setIsUpdating] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors, isDirty },
    reset,
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      dateOfBirth: '',
      bio: '',
    },
  })

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/account/profile')
    }
  }, [isAuthenticated, router])

  // Populate form with user data
  useEffect(() => {
    if (user) {
      reset({
        firstName: user.first_name || '',
        lastName: user.last_name || '',
        email: user.email || '',
        phone: user.phone || '',
        dateOfBirth: user.date_of_birth || '',
        bio: user.bio || '',
      })
    }
  }, [user, reset])

  const onSubmit = async (data: ProfileFormData) => {
    setIsUpdating(true)
    try {
      await updateProfile({
        first_name: data.firstName,
        last_name: data.lastName,
        email: data.email,
        phone: data.phone,
        date_of_birth: data.dateOfBirth,
        bio: data.bio,
      })

      addNotification({
        type: 'success',
        title: 'Profile updated',
        message: 'Your profile has been successfully updated.',
      })

      // Reset form dirty state
      reset(data)
    } catch (error: any) {
      addNotification({
        type: 'error',
        title: 'Update failed',
        message: error.message || 'Failed to update profile. Please try again.',
      })
    } finally {
      setIsUpdating(false)
    }
  }

  if (!isAuthenticated || !user) {
    return null // Will redirect
  }

  return (
    <MainLayout>
      <div className={styles.profilePage}>
        <div className={styles.container}>
          {/* Header */}
          <div className={styles.header}>
            <Button
              variant="ghost"
              leftIcon={<FiArrowLeft size={18} />}
              href="/account"
              className={styles.backButton}
            >
              Back to Account
            </Button>
            
            <div className={styles.titleSection}>
              <h1 className={styles.title}>Edit Profile</h1>
              <p className={styles.subtitle}>
                Update your personal information and preferences
              </p>
            </div>
          </div>

          <div className={styles.content}>
            {/* Profile Form */}
            <div className={styles.profileForm}>
              <div className={styles.formHeader}>
                <h2 className={styles.formTitle}>Personal Information</h2>
                <p className={styles.formDescription}>
                  This information will be used for your orders and account management.
                </p>
              </div>

              <form onSubmit={handleSubmit(onSubmit)} className={styles.form}>
                <div className={styles.row}>
                  <Input
                    {...register('firstName')}
                    label="First Name"
                    placeholder="Enter your first name"
                    error={errors.firstName?.message}
                    leftIcon={<FiUser size={18} />}
                    fullWidth
                  />
                  <Input
                    {...register('lastName')}
                    label="Last Name"
                    placeholder="Enter your last name"
                    error={errors.lastName?.message}
                    leftIcon={<FiUser size={18} />}
                    fullWidth
                  />
                </div>

                <Input
                  {...register('email')}
                  type="email"
                  label="Email Address"
                  placeholder="Enter your email address"
                  error={errors.email?.message}
                  leftIcon={<FiMail size={18} />}
                  fullWidth
                />

                <div className={styles.row}>
                  <Input
                    {...register('phone')}
                    type="tel"
                    label="Phone Number (Optional)"
                    placeholder="Enter your phone number"
                    error={errors.phone?.message}
                    leftIcon={<FiPhone size={18} />}
                    fullWidth
                  />
                  <Input
                    {...register('dateOfBirth')}
                    type="date"
                    label="Date of Birth (Optional)"
                    error={errors.dateOfBirth?.message}
                    leftIcon={<FiCalendar size={18} />}
                    fullWidth
                  />
                </div>

                <div className={styles.textareaField}>
                  <label className={styles.label}>Bio (Optional)</label>
                  <textarea
                    {...register('bio')}
                    placeholder="Tell us a little about yourself..."
                    className={styles.textarea}
                    rows={4}
                  />
                  {errors.bio && (
                    <span className={styles.error}>{errors.bio.message}</span>
                  )}
                </div>

                <div className={styles.formActions}>
                  <Button
                    type="button"
                    variant="secondary"
                    onClick={() => router.back()}
                  >
                    Cancel
                  </Button>
                  
                  <Button
                    type="submit"
                    variant="primary"
                    loading={isUpdating}
                    disabled={!isDirty}
                    leftIcon={<FiSave size={18} />}
                  >
                    Save Changes
                  </Button>
                </div>
              </form>
            </div>

            {/* Account Info */}
            <div className={styles.accountInfo}>
              <div className={styles.infoCard}>
                <h3 className={styles.infoTitle}>Account Information</h3>
                <div className={styles.infoList}>
                  <div className={styles.infoItem}>
                    <span className={styles.infoLabel}>Member Since:</span>
                    <span className={styles.infoValue}>
                      {new Date(user.date_joined).toLocaleDateString()}
                    </span>
                  </div>
                  <div className={styles.infoItem}>
                    <span className={styles.infoLabel}>Account Status:</span>
                    <span className={`${styles.infoValue} ${styles.active}`}>
                      Active
                    </span>
                  </div>
                  <div className={styles.infoItem}>
                    <span className={styles.infoLabel}>Email Verified:</span>
                    <span className={`${styles.infoValue} ${user.is_email_verified ? styles.verified : styles.unverified}`}>
                      {user.is_email_verified ? 'Verified' : 'Not Verified'}
                    </span>
                  </div>
                </div>
              </div>

              <div className={styles.infoCard}>
                <h3 className={styles.infoTitle}>Quick Actions</h3>
                <div className={styles.actionsList}>
                  <Button
                    variant="secondary"
                    size="sm"
                    href="/account/settings"
                    fullWidth
                  >
                    Account Settings
                  </Button>
                  <Button
                    variant="secondary"
                    size="sm"
                    href="/account/addresses"
                    fullWidth
                  >
                    Manage Addresses
                  </Button>
                  <Button
                    variant="secondary"
                    size="sm"
                    href="/account/orders"
                    fullWidth
                  >
                    View Orders
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
