@use '../../../../src/styles/variables' as *;
@use '../../../../src/styles/mixins' as *;

.profilePage {
  min-height: 100vh;
  background: $gray-50;
  padding: $spacing-6 0 $spacing-20;
}

.container {
  @include container;
  max-width: 1000px;
}

// Header
.header {
  margin-bottom: $spacing-8;
}

.backButton {
  margin-bottom: $spacing-4;
}

.titleSection {
  text-align: center;

  @include tablet-up {
    text-align: left;
  }
}

.title {
  font-size: clamp(1.75rem, 4vw, 2.5rem);
  font-weight: 700;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-2;
}

.subtitle {
  font-size: $font-size-2;
  color: $primary-lighter-text-color;
  line-height: 1.5;
}

// Content
.content {
  display: grid;
  grid-template-columns: 1fr;
  gap: $spacing-8;

  @include tablet-up {
    grid-template-columns: 2fr 1fr;
  }
}

// Profile Form
.profileForm {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-8;
  box-shadow: $box-shadow-sm;

  @include mobile-only {
    padding: $spacing-6;
  }
}

.formHeader {
  margin-bottom: $spacing-8;
  text-align: center;

  @include tablet-up {
    text-align: left;
  }
}

.formTitle {
  font-size: $font-size-4;
  font-weight: 700;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-3;
}

.formDescription {
  font-size: $font-size-2;
  color: $primary-lighter-text-color;
  line-height: 1.5;
}

.form {
  display: flex;
  flex-direction: column;
  gap: $spacing-6;
}

.row {
  display: grid;
  grid-template-columns: 1fr;
  gap: $spacing-4;

  @include tablet-up {
    grid-template-columns: 1fr 1fr;
  }
}

.textareaField {
  display: flex;
  flex-direction: column;
  gap: $spacing-2;
}

.label {
  font-size: $font-size-2;
  font-weight: 600;
  color: $primary-dark-text-color;
}

.textarea {
  padding: $spacing-3;
  border: 1px solid $gray-300;
  border-radius: $border-radius-md;
  font-size: $font-size-2;
  color: $primary-dark-text-color;
  background: white;
  resize: vertical;
  min-height: 100px;
  transition: border-color $transition-fast;

  &:focus {
    outline: none;
    border-color: $primary-blue;
    box-shadow: 0 0 0 3px rgba($primary-blue, 0.1);
  }

  &::placeholder {
    color: $primary-lighter-text-color;
  }
}

.error {
  font-size: $font-size-1;
  color: $error;
}

.formActions {
  @include flexbox(space-between, center);
  gap: $spacing-4;
  padding-top: $spacing-6;
  border-top: 1px solid $gray-200;

  @include mobile-only {
    flex-direction: column-reverse;
    
    > * {
      width: 100%;
    }
  }
}

// Account Info
.accountInfo {
  display: flex;
  flex-direction: column;
  gap: $spacing-6;
  height: fit-content;
}

.infoCard {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-6;
  box-shadow: $box-shadow-sm;
}

.infoTitle {
  font-size: $font-size-3;
  font-weight: 600;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-4;
}

.infoList {
  display: flex;
  flex-direction: column;
  gap: $spacing-3;
}

.infoItem {
  @include flexbox(space-between, center);
  gap: $spacing-3;
  padding: $spacing-2 0;
  border-bottom: 1px solid $gray-100;

  &:last-child {
    border-bottom: none;
  }
}

.infoLabel {
  font-size: $font-size-1;
  color: $primary-lighter-text-color;
  font-weight: 500;
}

.infoValue {
  font-size: $font-size-1;
  color: $primary-dark-text-color;
  font-weight: 600;
  text-align: right;

  &.active {
    color: $success;
  }

  &.verified {
    color: $success;
  }

  &.unverified {
    color: $warning;
  }
}

.actionsList {
  display: flex;
  flex-direction: column;
  gap: $spacing-3;
}

// Responsive adjustments
@include mobile-only {
  .profilePage {
    padding: $spacing-4 0 $spacing-16;
  }

  .content {
    gap: $spacing-6;
  }

  .row {
    gap: $spacing-3;
  }

  .infoCard {
    padding: $spacing-4;
  }

  .infoItem {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-1;
  }

  .infoValue {
    text-align: left;
  }
}
