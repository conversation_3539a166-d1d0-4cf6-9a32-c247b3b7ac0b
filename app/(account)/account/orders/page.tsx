'use client'

// Orders list page
// Displays user's order history with filtering and search

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { 
  FiPackage, 
  FiSearch, 
  FiFilter, 
  FiArrowLeft,
  FiEye,
  FiRefreshCw,
  FiDownload,
  FiCalendar
} from 'react-icons/fi'
import { MainLayout } from '../../../../src/components/layout/MainLayout'
import { Button } from '../../../../src/components/ui/Button'
import { Input } from '../../../../src/components/ui/Input'
import { useAuthStore } from '../../../../src/stores/auth-store'
import { formatDate, formatCurrency } from '../../../../src/lib/utils'
import styles from './page.module.scss'

// Mock order data - replace with actual API call
const mockOrders = [
  {
    id: '1',
    order_number: 'ORD-2024-001',
    status: 'delivered',
    total_amount: 129.99,
    created_at: '2024-01-15T10:30:00Z',
    items_count: 3,
    tracking_number: 'TRK123456789',
  },
  {
    id: '2',
    order_number: 'ORD-2024-002',
    status: 'shipped',
    total_amount: 89.50,
    created_at: '2024-01-20T14:15:00Z',
    items_count: 2,
    tracking_number: 'TRK987654321',
  },
  {
    id: '3',
    order_number: 'ORD-2024-003',
    status: 'processing',
    total_amount: 199.99,
    created_at: '2024-01-25T09:45:00Z',
    items_count: 5,
    tracking_number: null,
  },
]

const statusOptions = [
  { value: '', label: 'All Orders' },
  { value: 'pending', label: 'Pending' },
  { value: 'processing', label: 'Processing' },
  { value: 'shipped', label: 'Shipped' },
  { value: 'delivered', label: 'Delivered' },
  { value: 'cancelled', label: 'Cancelled' },
]

export default function OrdersPage() {
  const router = useRouter()
  const { user, isAuthenticated } = useAuthStore()
  const [orders, setOrders] = useState(mockOrders)
  const [filteredOrders, setFilteredOrders] = useState(mockOrders)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/account/orders')
    }
  }, [isAuthenticated, router])

  // Filter orders based on search and status
  useEffect(() => {
    let filtered = orders

    if (searchQuery) {
      filtered = filtered.filter(order =>
        order.order_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.tracking_number?.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    if (statusFilter) {
      filtered = filtered.filter(order => order.status === statusFilter)
    }

    setFilteredOrders(filtered)
  }, [orders, searchQuery, statusFilter])

  const handleReorder = async (orderId: string) => {
    setIsLoading(true)
    try {
      // TODO: Implement reorder functionality
      // await OrderService.reorder(orderId)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Redirect to cart or show success message
      router.push('/cart')
    } catch (error) {
      console.error('Reorder failed:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'warning'
      case 'processing': return 'info'
      case 'shipped': return 'primary'
      case 'delivered': return 'success'
      case 'cancelled': return 'error'
      default: return 'default'
    }
  }

  const getStatusLabel = (status: string) => {
    return status.replace('_', ' ').toUpperCase()
  }

  if (!isAuthenticated || !user) {
    return null // Will redirect
  }

  return (
    <MainLayout>
      <div className={styles.ordersPage}>
        <div className={styles.container}>
          {/* Header */}
          <div className={styles.header}>
            <Button
              variant="ghost"
              leftIcon={<FiArrowLeft size={18} />}
              href="/account"
              className={styles.backButton}
            >
              Back to Account
            </Button>
            
            <div className={styles.titleSection}>
              <h1 className={styles.title}>Order History</h1>
              <p className={styles.subtitle}>
                View and manage your orders
              </p>
            </div>
          </div>

          {/* Filters */}
          <div className={styles.filters}>
            <div className={styles.searchSection}>
              <Input
                type="text"
                placeholder="Search by order number or tracking..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                leftIcon={<FiSearch size={18} />}
                className={styles.searchInput}
              />
            </div>

            <div className={styles.filterSection}>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className={styles.statusFilter}
              >
                {statusOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Orders List */}
          <div className={styles.ordersSection}>
            {filteredOrders.length > 0 ? (
              <div className={styles.ordersList}>
                {filteredOrders.map((order) => (
                  <div key={order.id} className={styles.orderCard}>
                    <div className={styles.orderHeader}>
                      <div className={styles.orderInfo}>
                        <h3 className={styles.orderNumber}>
                          Order {order.order_number}
                        </h3>
                        <p className={styles.orderDate}>
                          <FiCalendar size={14} />
                          {formatDate(order.created_at, 'medium')}
                        </p>
                      </div>
                      
                      <div className={styles.orderStatus}>
                        <span className={`${styles.statusBadge} ${styles[getStatusColor(order.status)]}`}>
                          {getStatusLabel(order.status)}
                        </span>
                      </div>
                    </div>

                    <div className={styles.orderDetails}>
                      <div className={styles.orderMeta}>
                        <div className={styles.metaItem}>
                          <span className={styles.metaLabel}>Items:</span>
                          <span className={styles.metaValue}>{order.items_count}</span>
                        </div>
                        
                        <div className={styles.metaItem}>
                          <span className={styles.metaLabel}>Total:</span>
                          <span className={styles.metaValue}>
                            {formatCurrency(order.total_amount)}
                          </span>
                        </div>
                        
                        {order.tracking_number && (
                          <div className={styles.metaItem}>
                            <span className={styles.metaLabel}>Tracking:</span>
                            <span className={styles.metaValue}>
                              {order.tracking_number}
                            </span>
                          </div>
                        )}
                      </div>

                      <div className={styles.orderActions}>
                        <Button
                          variant="secondary"
                          size="sm"
                          leftIcon={<FiEye size={16} />}
                          href={`/account/orders/${order.id}`}
                        >
                          View Details
                        </Button>
                        
                        {order.status === 'delivered' && (
                          <Button
                            variant="secondary"
                            size="sm"
                            leftIcon={<FiRefreshCw size={16} />}
                            onClick={() => handleReorder(order.id)}
                            loading={isLoading}
                          >
                            Reorder
                          </Button>
                        )}
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          leftIcon={<FiDownload size={16} />}
                        >
                          Invoice
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className={styles.emptyState}>
                <div className={styles.emptyIcon}>📦</div>
                <h3>No orders found</h3>
                <p>
                  {searchQuery || statusFilter
                    ? 'Try adjusting your search or filter criteria.'
                    : 'You haven\'t placed any orders yet.'}
                </p>
                {!searchQuery && !statusFilter && (
                  <Button
                    variant="primary"
                    href="/products"
                    leftIcon={<FiPackage size={18} />}
                  >
                    Start Shopping
                  </Button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
