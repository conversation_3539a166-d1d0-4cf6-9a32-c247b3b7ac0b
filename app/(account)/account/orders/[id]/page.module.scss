@use '../../../../../src/styles/variables' as *;
@use '../../../../../src/styles/mixins' as *;

.orderDetailsPage {
  min-height: 100vh;
  background: $gray-50;
  padding: $spacing-6 0 $spacing-20;
}

.container {
  @include container;
  max-width: 1200px;
}

.loadingContainer {
  @include flexbox(center, center);
  min-height: 50vh;
}

.spinner {
  font-size: $font-size-2;
  color: $primary-lighter-text-color;
}

// Header
.header {
  margin-bottom: $spacing-8;
}

.backButton {
  margin-bottom: $spacing-4;
}

.titleSection {
  @include flexbox(space-between, flex-start);
  gap: $spacing-4;
  margin-bottom: $spacing-4;

  @include mobile-only {
    flex-direction: column;
    gap: $spacing-3;
  }
}

.title {
  font-size: clamp(1.75rem, 4vw, 2.5rem);
  font-weight: 700;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-2;
}

.orderMeta {
  @include flexbox(flex-start, center);
  gap: $spacing-4;
  flex-wrap: wrap;

  @include mobile-only {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-2;
  }
}

.statusBadge {
  display: inline-block;
  padding: $spacing-2 $spacing-4;
  border-radius: $border-radius-full;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &.warning {
    background: $warning-bg;
    color: $warning-text;
  }

  &.info {
    background: $info-bg;
    color: $info-text;
  }

  &.primary {
    background: $sky-lighter-blue;
    color: $primary-blue;
  }

  &.success {
    background: $success-bg;
    color: $success-text;
  }

  &.error {
    background: $error-bg;
    color: $error-text;
  }

  &.default {
    background: $gray-100;
    color: $gray-600;
  }
}

.orderDate {
  @include flexbox(flex-start, center);
  gap: $spacing-1;
  font-size: $font-size-1;
  color: $primary-lighter-text-color;
}

.headerActions {
  @include flexbox(flex-end, center);
  gap: $spacing-3;

  @include mobile-only {
    width: 100%;
    justify-content: stretch;
    
    > * {
      flex: 1;
    }
  }
}

// Content
.content {
  display: grid;
  grid-template-columns: 1fr;
  gap: $spacing-8;

  @include tablet-up {
    grid-template-columns: 1fr 1fr;
  }
}

.sectionTitle {
  font-size: $font-size-4;
  font-weight: 700;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-6;
}

// Timeline
.timeline {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-6;
  box-shadow: $box-shadow-sm;
  height: fit-content;

  @include tablet-up {
    order: 2;
  }
}

.timelineList {
  position: relative;
  padding-left: $spacing-6;
  margin-bottom: $spacing-6;

  &::before {
    content: '';
    position: absolute;
    left: 12px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: $gray-200;
  }
}

.timelineItem {
  position: relative;
  margin-bottom: $spacing-6;

  &:last-child {
    margin-bottom: 0;
  }

  &.completed {
    .timelineIcon {
      background: $success;
      color: white;
      border-color: $success;
    }

    .timelineTitle {
      color: $primary-dark-text-color;
    }
  }
}

.timelineIcon {
  position: absolute;
  left: -30px;
  top: 0;
  @include flexbox(center, center);
  width: 24px;
  height: 24px;
  background: white;
  border: 2px solid $gray-300;
  border-radius: $border-radius-full;
  color: $gray-400;
}

.timelineContent {
  padding-left: $spacing-2;
}

.timelineTitle {
  font-size: $font-size-2;
  font-weight: 600;
  color: $gray-500;
  margin-bottom: $spacing-1;
}

.timelineDescription {
  font-size: $font-size-1;
  color: $primary-lighter-text-color;
  line-height: 1.5;
  margin-bottom: $spacing-1;
}

.timelineDate {
  font-size: 11px;
  color: $gray-500;
}

.trackingInfo {
  padding: $spacing-4;
  background: $sky-lighter-blue;
  border-radius: $border-radius-md;
  border: 1px solid $primary-blue;

  h3 {
    font-size: $font-size-2;
    font-weight: 600;
    color: $primary-blue;
    margin-bottom: $spacing-2;
  }

  p {
    font-size: $font-size-1;
    color: $primary-blue;
    margin-bottom: $spacing-3;
  }
}

// Order Items
.orderItems {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-6;
  box-shadow: $box-shadow-sm;

  @include tablet-up {
    order: 1;
  }
}

.itemsList {
  margin-bottom: $spacing-6;
}

.orderItem {
  @include flexbox(flex-start, center);
  gap: $spacing-4;
  padding: $spacing-4;
  border: 1px solid $gray-200;
  border-radius: $border-radius-md;
  margin-bottom: $spacing-3;

  &:last-child {
    margin-bottom: 0;
  }

  @include mobile-only {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-3;
  }
}

.itemImage {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: $border-radius-md;
  overflow: hidden;
  background: $gray-100;
  flex-shrink: 0;
}

.image {
  object-fit: cover;
  transition: transform $transition-fast;

  &:hover {
    transform: scale(1.05);
  }
}

.itemInfo {
  flex: 1;
  min-width: 0;
}

.itemName {
  font-size: $font-size-2;
  font-weight: 600;
  color: $primary-dark-text-color;
  text-decoration: none;
  line-height: 1.4;
  margin-bottom: $spacing-1;
  transition: color $transition-fast;

  &:hover {
    color: $primary-blue;
  }
}

.itemVariant {
  font-size: $font-size-1;
  color: $primary-lighter-text-color;
  margin-bottom: $spacing-1;
}

.itemQuantity {
  font-size: $font-size-1;
  color: $primary-lighter-text-color;
}

.itemPrice {
  font-size: $font-size-3;
  font-weight: 700;
  color: $primary-blue;
  flex-shrink: 0;

  @include mobile-only {
    align-self: flex-end;
  }
}

// Order Summary
.orderSummary {
  border-top: 1px solid $gray-200;
  padding-top: $spacing-4;
  margin-top: $spacing-4;
}

.summaryRow {
  @include flexbox(space-between, center);
  padding: $spacing-2 0;
  font-size: $font-size-2;
  color: $primary-lighter-text-color;

  span:last-child {
    font-weight: 600;
    color: $primary-dark-text-color;
  }
}

.summaryDivider {
  height: 1px;
  background: $gray-200;
  margin: $spacing-3 0;
}

.summaryTotal {
  @include flexbox(space-between, center);
  padding: $spacing-3 0;
  font-size: $font-size-3;
  font-weight: 700;
  color: $primary-dark-text-color;
  border-top: 2px solid $gray-200;

  span:last-child {
    color: $primary-blue;
    font-size: $font-size-4;
  }
}

// Order Details
.orderDetails {
  grid-column: 1 / -1;
}

.detailsGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: $spacing-6;

  @include tablet-up {
    grid-template-columns: 1fr 1fr;
  }
}

.detailCard {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-6;
  box-shadow: $box-shadow-sm;
}

.detailTitle {
  @include flexbox(flex-start, center);
  gap: $spacing-2;
  font-size: $font-size-3;
  font-weight: 600;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-4;

  svg {
    color: $primary-blue;
  }
}

.detailContent {
  color: $primary-lighter-text-color;
  line-height: 1.6;

  p {
    margin-bottom: $spacing-1;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// Responsive adjustments
@include mobile-only {
  .orderDetailsPage {
    padding: $spacing-4 0 $spacing-16;
  }

  .content {
    gap: $spacing-6;
  }

  .timeline,
  .orderItems,
  .detailCard {
    padding: $spacing-4;
  }

  .timelineList {
    padding-left: $spacing-4;
  }

  .timelineIcon {
    left: -24px;
    width: 20px;
    height: 20px;
  }

  .orderItem {
    padding: $spacing-3;
  }

  .itemImage {
    width: 60px;
    height: 60px;
  }
}
