'use client'

// Order details page
// Displays detailed information about a specific order

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import { 
  FiArrowLeft,
  FiPackage,
  FiTruck,
  FiMapPin,
  FiCreditCard,
  FiDownload,
  FiRefreshCw,
  FiExternalLink,
  FiCalendar,
  FiCheck
} from 'react-icons/fi'
import { MainLayout } from '../../../../../src/components/layout/MainLayout'
import { Button } from '../../../../../src/components/ui/Button'
import { useAuthStore } from '../../../../../src/stores/auth-store'
import { formatDate, formatCurrency } from '../../../../../src/lib/utils'
import styles from './page.module.scss'

interface OrderPageProps {
  params: {
    id: string
  }
}

// Mock order data - replace with actual API call
const mockOrder = {
  id: '1',
  order_number: 'ORD-2024-001',
  status: 'delivered',
  total_amount: 129.99,
  subtotal: 119.99,
  tax: 9.60,
  shipping: 0,
  created_at: '2024-01-15T10:30:00Z',
  delivered_at: '2024-01-18T14:30:00Z',
  tracking_number: 'TRK123456789',
  tracking_url: 'https://tracking.example.com/TRK123456789',
  shipping_address: {
    name: 'John Doe',
    address_line_1: '123 Main St',
    address_line_2: 'Apt 4B',
    city: 'New York',
    state: 'NY',
    zip_code: '10001',
    country: 'United States',
  },
  payment_method: {
    type: 'card',
    last_four: '4242',
    brand: 'Visa',
  },
  items: [
    {
      id: '1',
      product: {
        id: '1',
        name: 'Premium Wireless Headphones',
        slug: 'premium-wireless-headphones',
        images: [{ image: '/api/placeholder/300/300', alt_text: 'Headphones' }],
      },
      variant: {
        id: '1',
        price: 79.99,
        attributes: [{ name: 'Color', value: 'Black' }],
      },
      quantity: 1,
    },
    {
      id: '2',
      product: {
        id: '2',
        name: 'Smartphone Case',
        slug: 'smartphone-case',
        images: [{ image: '/api/placeholder/300/300', alt_text: 'Phone Case' }],
      },
      variant: {
        id: '2',
        price: 39.99,
        attributes: [{ name: 'Color', value: 'Blue' }, { name: 'Size', value: 'iPhone 15' }],
      },
      quantity: 1,
    },
  ],
  timeline: [
    {
      status: 'order_placed',
      title: 'Order Placed',
      description: 'Your order has been received and is being processed.',
      timestamp: '2024-01-15T10:30:00Z',
      completed: true,
    },
    {
      status: 'payment_confirmed',
      title: 'Payment Confirmed',
      description: 'Payment has been successfully processed.',
      timestamp: '2024-01-15T10:35:00Z',
      completed: true,
    },
    {
      status: 'processing',
      title: 'Processing',
      description: 'Your order is being prepared for shipment.',
      timestamp: '2024-01-16T09:00:00Z',
      completed: true,
    },
    {
      status: 'shipped',
      title: 'Shipped',
      description: 'Your order has been shipped and is on its way.',
      timestamp: '2024-01-17T11:20:00Z',
      completed: true,
    },
    {
      status: 'delivered',
      title: 'Delivered',
      description: 'Your order has been successfully delivered.',
      timestamp: '2024-01-18T14:30:00Z',
      completed: true,
    },
  ],
}

export default function OrderDetailsPage({ params }: OrderPageProps) {
  const router = useRouter()
  const { user, isAuthenticated } = useAuthStore()
  const [order, setOrder] = useState(mockOrder)
  const [isLoading, setIsLoading] = useState(false)

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/account/orders')
    }
  }, [isAuthenticated, router])

  // Load order data
  useEffect(() => {
    const loadOrder = async () => {
      setIsLoading(true)
      try {
        // TODO: Replace with actual API call
        // const orderData = await OrderService.getOrder(params.id)
        // setOrder(orderData)
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500))
      } catch (error) {
        console.error('Failed to load order:', error)
        router.push('/account/orders')
      } finally {
        setIsLoading(false)
      }
    }

    if (params.id) {
      loadOrder()
    }
  }, [params.id, router])

  const handleReorder = async () => {
    setIsLoading(true)
    try {
      // TODO: Implement reorder functionality
      // await OrderService.reorder(order.id)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      router.push('/cart')
    } catch (error) {
      console.error('Reorder failed:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'warning'
      case 'processing': return 'info'
      case 'shipped': return 'primary'
      case 'delivered': return 'success'
      case 'cancelled': return 'error'
      default: return 'default'
    }
  }

  if (!isAuthenticated || !user) {
    return null // Will redirect
  }

  if (isLoading) {
    return (
      <MainLayout>
        <div className={styles.loadingContainer}>
          <div className={styles.spinner}>Loading order details...</div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className={styles.orderDetailsPage}>
        <div className={styles.container}>
          {/* Header */}
          <div className={styles.header}>
            <Button
              variant="ghost"
              leftIcon={<FiArrowLeft size={18} />}
              href="/account/orders"
              className={styles.backButton}
            >
              Back to Orders
            </Button>
            
            <div className={styles.titleSection}>
              <h1 className={styles.title}>Order {order.order_number}</h1>
              <div className={styles.orderMeta}>
                <span className={`${styles.statusBadge} ${styles[getStatusColor(order.status)]}`}>
                  {order.status.replace('_', ' ').toUpperCase()}
                </span>
                <span className={styles.orderDate}>
                  <FiCalendar size={14} />
                  Placed on {formatDate(order.created_at, 'medium')}
                </span>
              </div>
            </div>

            <div className={styles.headerActions}>
              <Button
                variant="secondary"
                leftIcon={<FiDownload size={18} />}
              >
                Download Invoice
              </Button>
              
              {order.status === 'delivered' && (
                <Button
                  variant="primary"
                  leftIcon={<FiRefreshCw size={18} />}
                  onClick={handleReorder}
                  loading={isLoading}
                >
                  Reorder
                </Button>
              )}
            </div>
          </div>

          <div className={styles.content}>
            {/* Order Timeline */}
            <div className={styles.timeline}>
              <h2 className={styles.sectionTitle}>Order Status</h2>
              <div className={styles.timelineList}>
                {order.timeline.map((event, index) => (
                  <div 
                    key={event.status} 
                    className={`${styles.timelineItem} ${event.completed ? styles.completed : ''}`}
                  >
                    <div className={styles.timelineIcon}>
                      {event.completed ? <FiCheck size={16} /> : <div className={styles.dot} />}
                    </div>
                    <div className={styles.timelineContent}>
                      <h4 className={styles.timelineTitle}>{event.title}</h4>
                      <p className={styles.timelineDescription}>{event.description}</p>
                      {event.completed && (
                        <span className={styles.timelineDate}>
                          {formatDate(event.timestamp, 'medium')}
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {order.tracking_number && (
                <div className={styles.trackingInfo}>
                  <h3>Tracking Information</h3>
                  <p>Tracking Number: <strong>{order.tracking_number}</strong></p>
                  {order.tracking_url && (
                    <Button
                      variant="secondary"
                      size="sm"
                      leftIcon={<FiExternalLink size={16} />}
                      href={order.tracking_url}
                      target="_blank"
                    >
                      Track Package
                    </Button>
                  )}
                </div>
              )}
            </div>

            {/* Order Items */}
            <div className={styles.orderItems}>
              <h2 className={styles.sectionTitle}>Order Items</h2>
              <div className={styles.itemsList}>
                {order.items.map((item) => (
                  <div key={item.id} className={styles.orderItem}>
                    <Link 
                      href={`/products/${item.product.slug}`}
                      className={styles.itemImage}
                    >
                      <Image
                        src={item.product.images[0]?.image || '/api/placeholder/300/300'}
                        alt={item.product.images[0]?.alt_text || item.product.name}
                        fill
                        className={styles.image}
                      />
                    </Link>

                    <div className={styles.itemInfo}>
                      <Link 
                        href={`/products/${item.product.slug}`}
                        className={styles.itemName}
                      >
                        {item.product.name}
                      </Link>
                      
                      {item.variant.attributes.length > 0 && (
                        <div className={styles.itemVariant}>
                          {item.variant.attributes.map(attr => attr.value).join(' / ')}
                        </div>
                      )}

                      <div className={styles.itemQuantity}>
                        Quantity: {item.quantity}
                      </div>
                    </div>

                    <div className={styles.itemPrice}>
                      {formatCurrency(item.variant.price * item.quantity)}
                    </div>
                  </div>
                ))}
              </div>

              {/* Order Summary */}
              <div className={styles.orderSummary}>
                <div className={styles.summaryRow}>
                  <span>Subtotal</span>
                  <span>{formatCurrency(order.subtotal)}</span>
                </div>
                <div className={styles.summaryRow}>
                  <span>Shipping</span>
                  <span>{order.shipping > 0 ? formatCurrency(order.shipping) : 'Free'}</span>
                </div>
                <div className={styles.summaryRow}>
                  <span>Tax</span>
                  <span>{formatCurrency(order.tax)}</span>
                </div>
                <div className={styles.summaryDivider} />
                <div className={styles.summaryTotal}>
                  <span>Total</span>
                  <span>{formatCurrency(order.total_amount)}</span>
                </div>
              </div>
            </div>

            {/* Order Details */}
            <div className={styles.orderDetails}>
              <div className={styles.detailsGrid}>
                {/* Shipping Address */}
                <div className={styles.detailCard}>
                  <h3 className={styles.detailTitle}>
                    <FiMapPin size={20} />
                    Shipping Address
                  </h3>
                  <div className={styles.detailContent}>
                    <p>{order.shipping_address.name}</p>
                    <p>{order.shipping_address.address_line_1}</p>
                    {order.shipping_address.address_line_2 && (
                      <p>{order.shipping_address.address_line_2}</p>
                    )}
                    <p>
                      {order.shipping_address.city}, {order.shipping_address.state} {order.shipping_address.zip_code}
                    </p>
                    <p>{order.shipping_address.country}</p>
                  </div>
                </div>

                {/* Payment Method */}
                <div className={styles.detailCard}>
                  <h3 className={styles.detailTitle}>
                    <FiCreditCard size={20} />
                    Payment Method
                  </h3>
                  <div className={styles.detailContent}>
                    <p>{order.payment_method.brand} ending in {order.payment_method.last_four}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
