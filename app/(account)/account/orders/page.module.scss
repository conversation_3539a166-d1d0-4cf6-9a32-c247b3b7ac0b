@use '../../../../src/styles/variables' as *;
@use '../../../../src/styles/mixins' as *;

.ordersPage {
  min-height: 100vh;
  background: $gray-50;
  padding: $spacing-6 0 $spacing-20;
}

.container {
  @include container;
  max-width: 1000px;
}

// Header
.header {
  margin-bottom: $spacing-8;
}

.backButton {
  margin-bottom: $spacing-4;
}

.titleSection {
  text-align: center;

  @include tablet-up {
    text-align: left;
  }
}

.title {
  font-size: clamp(1.75rem, 4vw, 2.5rem);
  font-weight: 700;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-2;
}

.subtitle {
  font-size: $font-size-2;
  color: $primary-lighter-text-color;
  line-height: 1.5;
}

// Filters
.filters {
  display: grid;
  grid-template-columns: 1fr;
  gap: $spacing-4;
  margin-bottom: $spacing-8;
  padding: $spacing-4;
  background: white;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow-sm;

  @include tablet-up {
    grid-template-columns: 1fr auto;
    align-items: center;
  }
}

.searchSection {
  flex: 1;
}

.searchInput {
  width: 100%;
}

.filterSection {
  min-width: 200px;
}

.statusFilter {
  width: 100%;
  padding: $spacing-3;
  border: 1px solid $gray-300;
  border-radius: $border-radius-md;
  font-size: $font-size-2;
  color: $primary-dark-text-color;
  background: white;
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: $primary-blue;
    box-shadow: 0 0 0 3px rgba($primary-blue, 0.1);
  }
}

// Orders Section
.ordersSection {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-6;
  box-shadow: $box-shadow-sm;
}

.ordersList {
  display: flex;
  flex-direction: column;
  gap: $spacing-4;
}

.orderCard {
  border: 1px solid $gray-200;
  border-radius: $border-radius-lg;
  padding: $spacing-4;
  transition: all $transition-fast;

  &:hover {
    border-color: $primary-blue-light;
    box-shadow: $box-shadow-sm;
  }
}

.orderHeader {
  @include flexbox(space-between, flex-start);
  gap: $spacing-4;
  margin-bottom: $spacing-4;
  padding-bottom: $spacing-3;
  border-bottom: 1px solid $gray-100;

  @include mobile-only {
    flex-direction: column;
    gap: $spacing-2;
  }
}

.orderInfo {
  flex: 1;
}

.orderNumber {
  font-size: $font-size-3;
  font-weight: 600;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-1;
}

.orderDate {
  @include flexbox(flex-start, center);
  gap: $spacing-1;
  font-size: $font-size-1;
  color: $primary-lighter-text-color;
}

.orderStatus {
  flex-shrink: 0;
}

.statusBadge {
  display: inline-block;
  padding: $spacing-1 $spacing-3;
  border-radius: $border-radius-full;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &.warning {
    background: $warning-bg;
    color: $warning-text;
  }

  &.info {
    background: $info-bg;
    color: $info-text;
  }

  &.primary {
    background: $sky-lighter-blue;
    color: $primary-blue;
  }

  &.success {
    background: $success-bg;
    color: $success-text;
  }

  &.error {
    background: $error-bg;
    color: $error-text;
  }

  &.default {
    background: $gray-100;
    color: $gray-600;
  }
}

.orderDetails {
  @include flexbox(space-between, flex-end);
  gap: $spacing-4;

  @include mobile-only {
    flex-direction: column;
    align-items: stretch;
    gap: $spacing-3;
  }
}

.orderMeta {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: $spacing-3;
  flex: 1;

  @include mobile-only {
    grid-template-columns: 1fr 1fr;
  }
}

.metaItem {
  display: flex;
  flex-direction: column;
  gap: $spacing-1;
}

.metaLabel {
  font-size: $font-size-1;
  color: $primary-lighter-text-color;
  font-weight: 500;
}

.metaValue {
  font-size: $font-size-2;
  color: $primary-dark-text-color;
  font-weight: 600;
}

.orderActions {
  @include flexbox(flex-end, center);
  gap: $spacing-2;
  flex-shrink: 0;

  @include mobile-only {
    justify-content: stretch;
    
    > * {
      flex: 1;
    }
  }
}

// Empty State
.emptyState {
  @include flexbox(center, center);
  flex-direction: column;
  text-align: center;
  padding: $spacing-12 $spacing-4;
  border: 2px dashed $gray-300;
  border-radius: $border-radius-lg;
}

.emptyIcon {
  font-size: 4rem;
  margin-bottom: $spacing-4;
  opacity: 0.6;
}

.emptyState h3 {
  font-size: $font-size-3;
  font-weight: 600;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-2;
}

.emptyState p {
  font-size: $font-size-2;
  color: $primary-lighter-text-color;
  margin-bottom: $spacing-6;
  max-width: 400px;
}

// Responsive adjustments
@include mobile-only {
  .ordersPage {
    padding: $spacing-4 0 $spacing-16;
  }

  .filters,
  .ordersSection {
    padding: $spacing-4;
  }

  .orderCard {
    padding: $spacing-3;
  }

  .orderActions {
    flex-direction: column;
    
    > * {
      width: 100%;
    }
  }
}
