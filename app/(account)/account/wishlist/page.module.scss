@use '../../../../src/styles/variables' as *;
@use '../../../../src/styles/mixins' as *;

.wishlistPage {
  min-height: 100vh;
  background: $gray-50;
  padding: $spacing-6 0 $spacing-20;
}

.container {
  @include container;
  max-width: 1200px;
}

// Header
.header {
  @include flexbox(space-between, flex-start);
  gap: $spacing-4;
  margin-bottom: $spacing-8;

  @include mobile-only {
    flex-direction: column;
    gap: $spacing-3;
  }
}

.backButton {
  margin-bottom: $spacing-4;

  @include tablet-up {
    margin-bottom: 0;
    order: -1;
  }
}

.titleSection {
  flex: 1;
  text-align: center;

  @include tablet-up {
    text-align: left;
  }
}

.title {
  font-size: clamp(1.75rem, 4vw, 2.5rem);
  font-weight: 700;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-2;
}

.subtitle {
  font-size: $font-size-2;
  color: $primary-lighter-text-color;
  line-height: 1.5;
}

.headerActions {
  @include mobile-only {
    width: 100%;
  }
}

// Controls
.controls {
  @include flexbox(space-between, center);
  gap: $spacing-4;
  margin-bottom: $spacing-6;
  padding: $spacing-4;
  background: white;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow-sm;

  @include mobile-only {
    flex-direction: column;
    gap: $spacing-3;
  }
}

.viewControls {
  @include flexbox(center, center);
  gap: 0;
  border: 1px solid $gray-300;
  border-radius: $border-radius-md;
  overflow: hidden;
}

.viewButton {
  @include flexbox(center, center);
  width: 40px;
  height: 40px;
  border: none;
  background: white;
  color: $primary-lighter-text-color;
  cursor: pointer;
  transition: all $transition-fast;

  &:hover {
    background: $gray-100;
    color: $primary-dark-text-color;
  }

  &.active {
    background: $primary-blue;
    color: white;
  }
}

.sortControls {
  min-width: 200px;

  @include mobile-only {
    width: 100%;
  }
}

.sortSelect {
  width: 100%;
  padding: $spacing-3;
  border: 1px solid $gray-300;
  border-radius: $border-radius-md;
  font-size: $font-size-2;
  color: $primary-dark-text-color;
  background: white;
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: $primary-blue;
    box-shadow: 0 0 0 3px rgba($primary-blue, 0.1);
  }
}

// Wishlist Section
.wishlistSection {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-6;
  box-shadow: $box-shadow-sm;
}

.itemsGrid {
  display: grid;
  gap: $spacing-6;

  &.grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));

    @include mobile-only {
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: $spacing-4;
    }
  }

  &.list {
    grid-template-columns: 1fr;
    gap: $spacing-4;
  }
}

.wishlistItem {
  border: 1px solid $gray-200;
  border-radius: $border-radius-lg;
  overflow: hidden;
  transition: all $transition-fast;

  &:hover {
    border-color: $primary-blue-light;
    box-shadow: $box-shadow-sm;
  }

  .list & {
    display: grid;
    grid-template-columns: 120px 1fr;
    gap: $spacing-4;

    @include mobile-only {
      grid-template-columns: 100px 1fr;
      gap: $spacing-3;
    }
  }
}

.itemImage {
  position: relative;
  width: 100%;
  height: 200px;
  background: $gray-100;
  overflow: hidden;

  .list & {
    width: 120px;
    height: 120px;

    @include mobile-only {
      width: 100px;
      height: 100px;
    }
  }

  a {
    display: block;
    width: 100%;
    height: 100%;
  }
}

.image {
  object-fit: cover;
  transition: transform $transition-fast;

  &:hover {
    transform: scale(1.05);
  }
}

.removeButton {
  position: absolute;
  top: $spacing-2;
  right: $spacing-2;
  @include flexbox(center, center);
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: $border-radius-full;
  color: $error;
  cursor: pointer;
  transition: all $transition-fast;
  opacity: 0;

  .wishlistItem:hover & {
    opacity: 1;
  }

  &:hover {
    background: $error;
    color: white;
    transform: scale(1.1);
  }
}

.itemInfo {
  padding: $spacing-4;
  display: flex;
  flex-direction: column;
  gap: $spacing-2;

  .list & {
    padding: $spacing-3 $spacing-4;
    justify-content: space-between;
  }

  @include mobile-only {
    padding: $spacing-3;
  }
}

.itemName {
  font-size: $font-size-2;
  font-weight: 600;
  color: $primary-dark-text-color;
  text-decoration: none;
  line-height: 1.4;
  margin-bottom: $spacing-1;
  transition: color $transition-fast;

  &:hover {
    color: $primary-blue;
  }
}

.itemDescription {
  font-size: $font-size-1;
  color: $primary-lighter-text-color;
  line-height: 1.5;
  margin-bottom: $spacing-3;

  .list & {
    margin-bottom: $spacing-2;
  }
}

.itemMeta {
  margin-bottom: $spacing-3;

  .list & {
    margin-bottom: $spacing-2;
  }
}

.itemPrice {
  @include flexbox(flex-start, center);
  gap: $spacing-2;
  margin-bottom: $spacing-2;
}

.price {
  font-size: $font-size-3;
  font-weight: 700;
  color: $primary-blue;
}

.comparePrice {
  font-size: $font-size-2;
  color: $gray-500;
  text-decoration: line-through;
}

.itemRating {
  font-size: $font-size-1;
  color: $primary-lighter-text-color;
}

.itemActions {
  margin-top: auto;
}

// Empty State
.emptyState {
  @include flexbox(center, center);
  flex-direction: column;
  text-align: center;
  padding: $spacing-12 $spacing-4;
  border: 2px dashed $gray-300;
  border-radius: $border-radius-lg;
}

.emptyIcon {
  font-size: 4rem;
  margin-bottom: $spacing-4;
  opacity: 0.6;
}

.emptyState h3 {
  font-size: $font-size-3;
  font-weight: 600;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-2;
}

.emptyState p {
  font-size: $font-size-2;
  color: $primary-lighter-text-color;
  margin-bottom: $spacing-6;
  max-width: 400px;
}

// Responsive adjustments
@include mobile-only {
  .wishlistPage {
    padding: $spacing-4 0 $spacing-16;
  }

  .wishlistSection {
    padding: $spacing-4;
  }

  .itemsGrid.grid {
    grid-template-columns: 1fr;
  }

  .wishlistItem.list {
    grid-template-columns: 80px 1fr;
    gap: $spacing-2;
  }

  .removeButton {
    opacity: 1;
    top: $spacing-1;
    right: $spacing-1;
    width: 28px;
    height: 28px;
  }
}
