'use client'

// Wishlist page
// Displays user's saved favorite products

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import { 
  FiHeart, 
  FiShoppingCart, 
  FiTrash2, 
  FiArrowLeft,
  FiShare2,
  FiFilter,
  FiGrid,
  FiList
} from 'react-icons/fi'
import { MainLayout } from '../../../../src/components/layout/MainLayout'
import { Button } from '../../../../src/components/ui/Button'
import { useAuthStore } from '../../../../src/stores/auth-store'
import { useCartActions } from '../../../../src/stores/cart-store'
import { useUIActions } from '../../../../src/stores/ui-store'
import { formatCurrency } from '../../../../src/lib/utils'
import styles from './page.module.scss'

// Mock wishlist data - replace with actual API call
const mockWishlistItems = [
  {
    id: '1',
    product: {
      id: '1',
      name: 'Premium Wireless Headphones',
      slug: 'premium-wireless-headphones',
      short_description: 'High-quality wireless headphones with noise cancellation',
      min_price: 79.99,
      max_price: 99.99,
      average_rating: 4.5,
      review_count: 128,
      images: [{ image: '/api/placeholder/300/300', alt_text: 'Headphones', is_primary: true }],
      variants: [
        {
          id: '1',
          price: 79.99,
          compare_at_price: 99.99,
          is_active: true,
          inventory_quantity: 10,
          attributes: [{ name: 'Color', value: 'Black' }],
        }
      ],
      category: { name: 'Electronics', slug: 'electronics' },
      brand: { name: 'AudioTech', slug: 'audiotech' },
    },
    added_at: '2024-01-15T10:30:00Z',
  },
  {
    id: '2',
    product: {
      id: '2',
      name: 'Smartphone Case',
      slug: 'smartphone-case',
      short_description: 'Protective case for your smartphone',
      min_price: 29.99,
      max_price: 39.99,
      average_rating: 4.2,
      review_count: 64,
      images: [{ image: '/api/placeholder/300/300', alt_text: 'Phone Case', is_primary: true }],
      variants: [
        {
          id: '2',
          price: 29.99,
          compare_at_price: 39.99,
          is_active: true,
          inventory_quantity: 25,
          attributes: [{ name: 'Color', value: 'Blue' }, { name: 'Size', value: 'iPhone 15' }],
        }
      ],
      category: { name: 'Accessories', slug: 'accessories' },
      brand: { name: 'CasePro', slug: 'casepro' },
    },
    added_at: '2024-01-20T14:15:00Z',
  },
]

export default function WishlistPage() {
  const router = useRouter()
  const { user, isAuthenticated } = useAuthStore()
  const { addItem } = useCartActions()
  const { addNotification } = useUIActions()
  
  const [wishlistItems, setWishlistItems] = useState(mockWishlistItems)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [sortBy, setSortBy] = useState('added_date')
  const [isLoading, setIsLoading] = useState(false)

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/account/wishlist')
    }
  }, [isAuthenticated, router])

  // Load wishlist data
  useEffect(() => {
    const loadWishlist = async () => {
      setIsLoading(true)
      try {
        // TODO: Replace with actual API call
        // const wishlistData = await WishlistService.getWishlist()
        // setWishlistItems(wishlistData)
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500))
      } catch (error) {
        console.error('Failed to load wishlist:', error)
      } finally {
        setIsLoading(false)
      }
    }

    if (isAuthenticated) {
      loadWishlist()
    }
  }, [isAuthenticated])

  const handleRemoveFromWishlist = async (itemId: string) => {
    try {
      // TODO: Replace with actual API call
      // await WishlistService.removeItem(itemId)
      
      setWishlistItems(prev => prev.filter(item => item.id !== itemId))
      
      addNotification({
        type: 'success',
        title: 'Removed from wishlist',
        message: 'Item has been removed from your wishlist.',
      })
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Remove failed',
        message: 'Failed to remove item from wishlist.',
      })
    }
  }

  const handleAddToCart = async (item: any) => {
    try {
      const primaryVariant = item.product.variants[0]
      addItem(item.product, primaryVariant, 1)
      
      addNotification({
        type: 'success',
        title: 'Added to cart',
        message: `${item.product.name} has been added to your cart.`,
      })
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Add to cart failed',
        message: 'Failed to add item to cart.',
      })
    }
  }

  const handleShareWishlist = async () => {
    try {
      if (navigator.share) {
        await navigator.share({
          title: 'My Wishlist',
          text: 'Check out my wishlist!',
          url: window.location.href,
        })
      } else {
        // Fallback: copy to clipboard
        await navigator.clipboard.writeText(window.location.href)
        addNotification({
          type: 'success',
          title: 'Link copied',
          message: 'Wishlist link has been copied to clipboard.',
        })
      }
    } catch (error) {
      console.error('Share failed:', error)
    }
  }

  const sortedItems = [...wishlistItems].sort((a, b) => {
    switch (sortBy) {
      case 'name':
        return a.product.name.localeCompare(b.product.name)
      case 'price_low':
        return a.product.min_price - b.product.min_price
      case 'price_high':
        return b.product.min_price - a.product.min_price
      case 'added_date':
      default:
        return new Date(b.added_at).getTime() - new Date(a.added_at).getTime()
    }
  })

  if (!isAuthenticated || !user) {
    return null // Will redirect
  }

  return (
    <MainLayout>
      <div className={styles.wishlistPage}>
        <div className={styles.container}>
          {/* Header */}
          <div className={styles.header}>
            <Button
              variant="ghost"
              leftIcon={<FiArrowLeft size={18} />}
              href="/account"
              className={styles.backButton}
            >
              Back to Account
            </Button>
            
            <div className={styles.titleSection}>
              <h1 className={styles.title}>My Wishlist</h1>
              <p className={styles.subtitle}>
                {wishlistItems.length} {wishlistItems.length === 1 ? 'item' : 'items'} saved
              </p>
            </div>

            <div className={styles.headerActions}>
              <Button
                variant="secondary"
                leftIcon={<FiShare2 size={18} />}
                onClick={handleShareWishlist}
              >
                Share
              </Button>
            </div>
          </div>

          {/* Controls */}
          <div className={styles.controls}>
            <div className={styles.viewControls}>
              <button
                onClick={() => setViewMode('grid')}
                className={`${styles.viewButton} ${viewMode === 'grid' ? styles.active : ''}`}
              >
                <FiGrid size={18} />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`${styles.viewButton} ${viewMode === 'list' ? styles.active : ''}`}
              >
                <FiList size={18} />
              </button>
            </div>

            <div className={styles.sortControls}>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className={styles.sortSelect}
              >
                <option value="added_date">Recently Added</option>
                <option value="name">Name A-Z</option>
                <option value="price_low">Price: Low to High</option>
                <option value="price_high">Price: High to Low</option>
              </select>
            </div>
          </div>

          {/* Wishlist Items */}
          <div className={styles.wishlistSection}>
            {sortedItems.length > 0 ? (
              <div className={`${styles.itemsGrid} ${styles[viewMode]}`}>
                {sortedItems.map((item) => (
                  <div key={item.id} className={styles.wishlistItem}>
                    <div className={styles.itemImage}>
                      <Link href={`/products/${item.product.slug}`}>
                        <Image
                          src={item.product.images[0]?.image || '/api/placeholder/300/300'}
                          alt={item.product.images[0]?.alt_text || item.product.name}
                          fill
                          className={styles.image}
                        />
                      </Link>
                      
                      <button
                        onClick={() => handleRemoveFromWishlist(item.id)}
                        className={styles.removeButton}
                        title="Remove from wishlist"
                      >
                        <FiTrash2 size={16} />
                      </button>
                    </div>

                    <div className={styles.itemInfo}>
                      <Link 
                        href={`/products/${item.product.slug}`}
                        className={styles.itemName}
                      >
                        {item.product.name}
                      </Link>
                      
                      <p className={styles.itemDescription}>
                        {item.product.short_description}
                      </p>

                      <div className={styles.itemMeta}>
                        <div className={styles.itemPrice}>
                          <span className={styles.price}>
                            {formatCurrency(item.product.min_price)}
                          </span>
                          {item.product.variants[0]?.compare_at_price && (
                            <span className={styles.comparePrice}>
                              {formatCurrency(item.product.variants[0].compare_at_price)}
                            </span>
                          )}
                        </div>

                        {item.product.average_rating && (
                          <div className={styles.itemRating}>
                            ⭐ {item.product.average_rating.toFixed(1)} ({item.product.review_count})
                          </div>
                        )}
                      </div>

                      <div className={styles.itemActions}>
                        <Button
                          variant="primary"
                          size="sm"
                          leftIcon={<FiShoppingCart size={16} />}
                          onClick={() => handleAddToCart(item)}
                          disabled={!item.product.variants[0]?.is_active || item.product.variants[0]?.inventory_quantity === 0}
                          fullWidth={viewMode === 'list'}
                        >
                          Add to Cart
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className={styles.emptyState}>
                <div className={styles.emptyIcon}>❤️</div>
                <h3>Your wishlist is empty</h3>
                <p>
                  Save items you love to your wishlist and they'll appear here.
                </p>
                <Button
                  variant="primary"
                  href="/products"
                  leftIcon={<FiHeart size={18} />}
                >
                  Start Shopping
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
