@use '../../../src/styles/variables' as *;
@use '../../../src/styles/mixins' as *;

.accountPage {
  min-height: 100vh;
  background: $gray-50;
  padding: $spacing-6 0 $spacing-20;
}

.container {
  @include container;
  max-width: 1200px;
}

// Header
.header {
  display: grid;
  grid-template-columns: 1fr;
  gap: $spacing-6;
  margin-bottom: $spacing-8;

  @include tablet-up {
    grid-template-columns: 1fr auto;
    align-items: center;
  }
}

.welcomeSection {
  @include mobile-only {
    text-align: center;
  }
}

.title {
  font-size: clamp(1.75rem, 4vw, 2.5rem);
  font-weight: 700;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-2;
}

.subtitle {
  font-size: $font-size-2;
  color: $primary-lighter-text-color;
  line-height: 1.5;
}

.profileCard {
  @include flexbox(space-between, center);
  gap: $spacing-4;
  background: white;
  padding: $spacing-4;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow-sm;

  @include mobile-only {
    flex-direction: column;
    text-align: center;
  }
}

.profileInfo {
  @include flexbox(flex-start, center);
  gap: $spacing-3;

  @include mobile-only {
    flex-direction: column;
    text-align: center;
  }
}

.avatar {
  @include flexbox(center, center);
  width: 60px;
  height: 60px;
  border-radius: $border-radius-full;
  background: $primary-blue;
  color: white;
  font-size: $font-size-4;
  font-weight: 700;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.userDetails {
  h3 {
    font-size: $font-size-3;
    font-weight: 600;
    color: $primary-dark-text-color;
    margin-bottom: $spacing-1;
  }

  p {
    font-size: $font-size-1;
    color: $primary-lighter-text-color;
    margin-bottom: $spacing-1;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.memberSince {
  font-size: 11px !important;
  color: $gray-500 !important;
}

// Quick Stats
.quickStats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: $spacing-4;
  margin-bottom: $spacing-10;
}

.stat {
  @include flexbox(flex-start, center);
  gap: $spacing-3;
  background: white;
  padding: $spacing-4;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow-sm;
}

.statIcon {
  @include flexbox(center, center);
  width: 48px;
  height: 48px;
  background: $sky-lighter-blue;
  color: $primary-blue;
  border-radius: $border-radius-md;
  flex-shrink: 0;
}

.statContent {
  h4 {
    font-size: $font-size-4;
    font-weight: 700;
    color: $primary-dark-text-color;
    margin-bottom: $spacing-1;
  }

  p {
    font-size: $font-size-1;
    color: $primary-lighter-text-color;
  }
}

// Content
.content {
  display: grid;
  grid-template-columns: 1fr;
  gap: $spacing-8;

  @include tablet-up {
    grid-template-columns: 2fr 1fr;
  }
}

// Account Menu
.accountMenu {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-6;
  box-shadow: $box-shadow-sm;
  height: fit-content;
}

.sectionTitle {
  font-size: $font-size-4;
  font-weight: 700;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-6;
}

.menuGrid {
  display: flex;
  flex-direction: column;
  gap: $spacing-3;
}

.menuItem {
  @include flexbox(space-between, center);
  gap: $spacing-4;
  padding: $spacing-4;
  border: 1px solid $gray-200;
  border-radius: $border-radius-lg;
  text-decoration: none;
  transition: all $transition-fast;

  &:hover {
    border-color: $primary-blue;
    background: $sky-lighter-blue;
  }
}

.menuIcon {
  @include flexbox(center, center);
  width: 48px;
  height: 48px;
  background: $gray-100;
  color: $primary-lighter-text-color;
  border-radius: $border-radius-md;
  flex-shrink: 0;

  .menuItem:hover & {
    background: $primary-blue;
    color: white;
  }
}

.menuContent {
  flex: 1;
  text-align: left;
}

.menuTitle {
  font-size: $font-size-2;
  font-weight: 600;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-1;
}

.menuDescription {
  font-size: $font-size-1;
  color: $primary-lighter-text-color;
  margin-bottom: $spacing-1;
}

.menuCount {
  font-size: 11px;
  color: $gray-500;
}

.menuArrow {
  color: $primary-lighter-text-color;
  flex-shrink: 0;

  .menuItem:hover & {
    color: $primary-blue;
  }
}

// Recent Orders
.recentOrders {
  @include tablet-up {
    order: -1;
  }
}

.sectionHeader {
  @include flexbox(space-between, center);
  margin-bottom: $spacing-6;
}

.viewAllLink {
  @include flexbox(center, center);
  gap: $spacing-1;
  color: $primary-blue;
  text-decoration: none;
  font-size: $font-size-1;
  font-weight: 500;
  transition: color $transition-fast;

  &:hover {
    color: $primary-blue-dark;
  }
}

.ordersList {
  display: flex;
  flex-direction: column;
  gap: $spacing-3;
}

.orderItem {
  @include flexbox(space-between, center);
  gap: $spacing-4;
  padding: $spacing-4;
  background: white;
  border: 1px solid $gray-200;
  border-radius: $border-radius-lg;
  text-decoration: none;
  transition: all $transition-fast;

  &:hover {
    border-color: $primary-blue;
    box-shadow: $box-shadow-sm;
  }
}

.orderInfo {
  flex: 1;
}

.orderNumber {
  font-size: $font-size-2;
  font-weight: 600;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-1;
}

.orderDate {
  font-size: $font-size-1;
  color: $primary-lighter-text-color;
  margin-bottom: $spacing-1;
}

.orderStatus {
  font-size: $font-size-1;
  color: $primary-lighter-text-color;
}

.status {
  font-weight: 600;
  
  &.pending {
    color: $warning;
  }
  
  &.processing {
    color: $primary-blue;
  }
  
  &.shipped {
    color: $info;
  }
  
  &.delivered {
    color: $success;
  }
  
  &.cancelled {
    color: $error;
  }
}

.orderTotal {
  font-size: $font-size-3;
  font-weight: 700;
  color: $primary-blue;
  flex-shrink: 0;
}

// Empty State
.emptyState {
  @include flexbox(center, center);
  flex-direction: column;
  text-align: center;
  padding: $spacing-12 $spacing-4;
  background: white;
  border-radius: $border-radius-lg;
  border: 2px dashed $gray-300;
}

.emptyIcon {
  font-size: 4rem;
  margin-bottom: $spacing-4;
  opacity: 0.6;
}

.emptyState h3 {
  font-size: $font-size-3;
  font-weight: 600;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-2;
}

.emptyState p {
  font-size: $font-size-2;
  color: $primary-lighter-text-color;
  margin-bottom: $spacing-6;
}

// Responsive adjustments
@include mobile-only {
  .accountPage {
    padding: $spacing-4 0 $spacing-16;
  }

  .quickStats {
    grid-template-columns: 1fr;
  }

  .content {
    gap: $spacing-6;
  }

  .accountMenu,
  .recentOrders {
    padding: $spacing-4;
  }

  .orderItem {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-3;
  }

  .orderTotal {
    align-self: flex-end;
  }
}
