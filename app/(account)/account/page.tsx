'use client'

// Account dashboard page
// Main user account overview with quick access to account features

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { 
  FiUser, 
  FiPackage, 
  FiMapPin, 
  FiHeart, 
  FiSettings,
  FiShoppingBag,
  FiCreditCard,
  FiArrowRight,
  FiEdit3
} from 'react-icons/fi'
import { MainLayout } from '../../../src/components/layout/MainLayout'
import { Button } from '../../../src/components/ui/Button'
import { useAuthStore } from '../../../src/stores/auth-store'
import { formatDate } from '../../../src/lib/utils'
import styles from './page.module.scss'

export default function AccountPage() {
  const router = useRouter()
  const { user, isAuthenticated } = useAuthStore()

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/account')
    }
  }, [isAuthenticated, router])

  if (!isAuthenticated || !user) {
    return null // Will redirect
  }

  const accountMenuItems = [
    {
      icon: <FiPackage size={24} />,
      title: 'Orders',
      description: 'View and track your orders',
      href: '/account/orders',
      count: user.order_count || 0,
    },
    {
      icon: <FiMapPin size={24} />,
      title: 'Addresses',
      description: 'Manage shipping addresses',
      href: '/account/addresses',
      count: user.addresses?.length || 0,
    },
    {
      icon: <FiHeart size={24} />,
      title: 'Wishlist',
      description: 'Your saved items',
      href: '/account/wishlist',
      count: user.wishlist_count || 0,
    },
    {
      icon: <FiCreditCard size={24} />,
      title: 'Payment Methods',
      description: 'Manage payment options',
      href: '/account/payment-methods',
      count: user.payment_methods?.length || 0,
    },
    {
      icon: <FiSettings size={24} />,
      title: 'Settings',
      description: 'Account preferences',
      href: '/account/settings',
      count: null,
    },
  ]

  const recentOrders = user.recent_orders || []

  return (
    <MainLayout>
      <div className={styles.accountPage}>
        <div className={styles.container}>
          {/* Header */}
          <div className={styles.header}>
            <div className={styles.welcomeSection}>
              <h1 className={styles.title}>
                Welcome back, {user.first_name || user.email}!
              </h1>
              <p className={styles.subtitle}>
                Manage your account and track your orders
              </p>
            </div>
            
            <div className={styles.profileCard}>
              <div className={styles.profileInfo}>
                <div className={styles.avatar}>
                  {user.avatar ? (
                    <img src={user.avatar} alt={user.first_name} />
                  ) : (
                    <span>{(user.first_name?.[0] || user.email[0]).toUpperCase()}</span>
                  )}
                </div>
                <div className={styles.userDetails}>
                  <h3>{user.first_name} {user.last_name}</h3>
                  <p>{user.email}</p>
                  <p className={styles.memberSince}>
                    Member since {formatDate(user.date_joined, 'medium')}
                  </p>
                </div>
              </div>
              <Button
                variant="secondary"
                size="sm"
                leftIcon={<FiEdit3 size={16} />}
                href="/account/profile"
              >
                Edit Profile
              </Button>
            </div>
          </div>

          {/* Quick Stats */}
          <div className={styles.quickStats}>
            <div className={styles.stat}>
              <div className={styles.statIcon}>
                <FiShoppingBag size={20} />
              </div>
              <div className={styles.statContent}>
                <h4>{user.order_count || 0}</h4>
                <p>Total Orders</p>
              </div>
            </div>
            
            <div className={styles.stat}>
              <div className={styles.statIcon}>
                <FiHeart size={20} />
              </div>
              <div className={styles.statContent}>
                <h4>{user.wishlist_count || 0}</h4>
                <p>Wishlist Items</p>
              </div>
            </div>
            
            <div className={styles.stat}>
              <div className={styles.statIcon}>
                <FiMapPin size={20} />
              </div>
              <div className={styles.statContent}>
                <h4>{user.addresses?.length || 0}</h4>
                <p>Saved Addresses</p>
              </div>
            </div>
          </div>

          <div className={styles.content}>
            {/* Account Menu */}
            <div className={styles.accountMenu}>
              <h2 className={styles.sectionTitle}>Account Management</h2>
              <div className={styles.menuGrid}>
                {accountMenuItems.map((item) => (
                  <Link key={item.href} href={item.href} className={styles.menuItem}>
                    <div className={styles.menuIcon}>
                      {item.icon}
                    </div>
                    <div className={styles.menuContent}>
                      <h3 className={styles.menuTitle}>{item.title}</h3>
                      <p className={styles.menuDescription}>{item.description}</p>
                      {item.count !== null && (
                        <span className={styles.menuCount}>{item.count} items</span>
                      )}
                    </div>
                    <div className={styles.menuArrow}>
                      <FiArrowRight size={20} />
                    </div>
                  </Link>
                ))}
              </div>
            </div>

            {/* Recent Orders */}
            <div className={styles.recentOrders}>
              <div className={styles.sectionHeader}>
                <h2 className={styles.sectionTitle}>Recent Orders</h2>
                <Link href="/account/orders" className={styles.viewAllLink}>
                  View All Orders
                  <FiArrowRight size={16} />
                </Link>
              </div>

              {recentOrders.length > 0 ? (
                <div className={styles.ordersList}>
                  {recentOrders.slice(0, 3).map((order) => (
                    <Link 
                      key={order.id} 
                      href={`/account/orders/${order.id}`}
                      className={styles.orderItem}
                    >
                      <div className={styles.orderInfo}>
                        <h4 className={styles.orderNumber}>Order #{order.order_number}</h4>
                        <p className={styles.orderDate}>
                          {formatDate(order.created_at, 'medium')}
                        </p>
                        <p className={styles.orderStatus}>
                          Status: <span className={`${styles.status} ${styles[order.status]}`}>
                            {order.status.replace('_', ' ').toUpperCase()}
                          </span>
                        </p>
                      </div>
                      <div className={styles.orderTotal}>
                        ${order.total_amount}
                      </div>
                    </Link>
                  ))}
                </div>
              ) : (
                <div className={styles.emptyState}>
                  <div className={styles.emptyIcon}>📦</div>
                  <h3>No orders yet</h3>
                  <p>When you place your first order, it will appear here.</p>
                  <Button
                    variant="primary"
                    href="/products"
                    leftIcon={<FiShoppingBag size={18} />}
                  >
                    Start Shopping
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
