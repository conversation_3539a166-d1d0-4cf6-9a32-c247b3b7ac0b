// import { Metadata } from 'next'
// import { notFound } from 'next/navigation'
// import { BrandPageClient } from './BrandPageClient'

// // This would typically fetch from your API
// async function getBrand(slug: string) {
//   // For now, return null to trigger client-side fetching
//   // In a real app, you'd fetch the brand here for SSR
//   return null
// }

// interface BrandPageProps {
//   params: {
//     slug: string
//   }
// }

// export async function generateMetadata({ params }: BrandPageProps): Promise<Metadata> {
//   const brand = await getBrand(params.slug)
  
//   if (!brand) {
//     return {
//       title: 'Brand | Picky Store',
//       description: 'Browse products by brand',
//     }
//   }

//   return {
//     title: `${brand.name} | Picky Store`,
//     description: brand.description || `Shop ${brand.name} products at Picky Store`,
//     openGraph: {
//       title: brand.name,
//       description: brand.description || `Shop ${brand.name} products`,
//       images: brand.logo ? [brand.logo] : [],
//     },
//   }
// }

// export default function BrandPage({ params }: BrandPageProps) {
//   return <BrandPageClient slug={params.slug} />
// }
