// 'use client'

// // Brand page client component
// // Displays products filtered by brand with sorting and filtering

// import { useState, useEffect } from 'react'
// import { notFound } from 'next/navigation'
// import Image from 'next/image'
// import Link from 'next/link'
// import { 
//   FiGrid, 
//   FiList, 
//   FiFilter,
//   FiSliders,
//   FiX,
//   FiChevronRight,
//   FiExternalLink
// } from 'react-icons/fi'
// import { MainLayout } from '../../../../src/components/layout/MainLayout'
// import { ProductCard } from '../../../../src/components/features/ProductCard'
// import { ProductFilters } from '../../../../src/components/features/ProductFilters'
// import { LoadingSpinner } from '../../../../src/components/ui/LoadingSpinner'
// import { Button } from '../../../../src/components/ui/Button'
// import { useBrand } from '../../../../src/hooks/queries/useBrands'
// import { useProducts } from '../../../../src/hooks/queries/useProducts'
// import { useFilterStore, useFilterActions } from '../../../../src/stores/filter-store'
// import styles from './BrandPageClient.module.scss'

// interface BrandPageClientProps {
//   slug: string
// }

// export function BrandPageClient({ slug }: BrandPageClientProps) {
//   const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
//   const [showFilters, setShowFilters] = useState(false)
//   const [sortBy, setSortBy] = useState('name')

//   const { data: brand, isLoading: brandLoading, error: brandError } = useBrand(slug)
//   const { filters } = useFilterStore()
//   const { setBrand: setFilterBrand, clearFilters } = useFilterActions()

//   // Set brand filter when brand loads
//   useEffect(() => {
//     if (brand) {
//       setFilterBrand(brand.id)
//     }
//     return () => {
//       clearFilters()
//     }
//   }, [brand, setFilterBrand, clearFilters])

//   // Get products for this brand
//   const { 
//     data: productsData, 
//     isLoading: productsLoading, 
//     error: productsError,
//     fetchNextPage,
//     hasNextPage,
//     isFetchingNextPage
//   } = useProducts({
//     ...filters,
//     brand: brand?.id,
//     ordering: sortBy,
//   })

//   const products = productsData?.pages.flatMap(page => page.results) || []
//   const totalProducts = productsData?.pages[0]?.count || 0

//   const sortOptions = [
//     { value: 'name', label: 'Name A-Z' },
//     { value: '-name', label: 'Name Z-A' },
//     { value: 'price', label: 'Price: Low to High' },
//     { value: '-price', label: 'Price: High to Low' },
//     { value: '-created_at', label: 'Newest First' },
//     { value: 'created_at', label: 'Oldest First' },
//     { value: '-average_rating', label: 'Highest Rated' },
//   ]

//   if (brandLoading) {
//     return (
//       <MainLayout>
//         <div className={styles.loadingContainer}>
//           <LoadingSpinner size="lg" />
//         </div>
//       </MainLayout>
//     )
//   }

//   if (brandError || !brand) {
//     notFound()
//   }

//   return (
//     <MainLayout>
//       <div className={styles.brandPage}>
//         <div className={styles.container}>
//           {/* Breadcrumb */}
//           <nav className={styles.breadcrumb}>
//             <Link href="/">Home</Link>
//             <FiChevronRight size={14} />
//             <Link href="/brands">Brands</Link>
//             <FiChevronRight size={14} />
//             <span>{brand.name}</span>
//           </nav>

//           {/* Brand Header */}
//           <div className={styles.brandHeader}>
//             {brand.logo && (
//               <div className={styles.brandLogo}>
//                 <Image
//                   src={brand.logo}
//                   alt={brand.name}
//                   fill
//                   className={styles.logo}
//                   priority
//                 />
//               </div>
//             )}
            
//             <div className={styles.brandInfo}>
//               <h1 className={styles.brandTitle}>{brand.name}</h1>
//               {brand.description && (
//                 <p className={styles.brandDescription}>
//                   {brand.description}
//                 </p>
//               )}
              
//               <div className={styles.brandMeta}>
//                 <p className={styles.productCount}>
//                   {totalProducts} {totalProducts === 1 ? 'product' : 'products'}
//                 </p>
                
//                 {brand.website && (
//                   <a 
//                     href={brand.website}
//                     target="_blank"
//                     rel="noopener noreferrer"
//                     className={styles.brandWebsite}
//                   >
//                     Visit Website
//                     <FiExternalLink size={16} />
//                   </a>
//                 )}
//               </div>
//             </div>
//           </div>

//           {/* Controls */}
//           <div className={styles.controls}>
//             <div className={styles.controlsLeft}>
//               <button
//                 onClick={() => setShowFilters(!showFilters)}
//                 className={`${styles.filterToggle} ${showFilters ? styles.active : ''}`}
//               >
//                 <FiSliders size={18} />
//                 Filters
//               </button>
//             </div>

//             <div className={styles.controlsRight}>
//               <div className={styles.viewControls}>
//                 <button
//                   onClick={() => setViewMode('grid')}
//                   className={`${styles.viewButton} ${viewMode === 'grid' ? styles.active : ''}`}
//                 >
//                   <FiGrid size={18} />
//                 </button>
//                 <button
//                   onClick={() => setViewMode('list')}
//                   className={`${styles.viewButton} ${viewMode === 'list' ? styles.active : ''}`}
//                 >
//                   <FiList size={18} />
//                 </button>
//               </div>

//               <select
//                 value={sortBy}
//                 onChange={(e) => setSortBy(e.target.value)}
//                 className={styles.sortSelect}
//               >
//                 {sortOptions.map(option => (
//                   <option key={option.value} value={option.value}>
//                     {option.label}
//                   </option>
//                 ))}
//               </select>
//             </div>
//           </div>

//           <div className={styles.brandContent}>
//             {/* Filters Sidebar */}
//             {showFilters && (
//               <div className={styles.filtersSidebar}>
//                 <div className={styles.filtersHeader}>
//                   <h3>Filters</h3>
//                   <button
//                     onClick={() => setShowFilters(false)}
//                     className={styles.closeFilters}
//                   >
//                     <FiX size={18} />
//                   </button>
//                 </div>
//                 <ProductFilters />
//               </div>
//             )}

//             {/* Products Grid */}
//             <div className={styles.productsSection}>
//               {productsLoading ? (
//                 <div className={styles.loadingContainer}>
//                   <LoadingSpinner size="lg" />
//                   <p>Loading products...</p>
//                 </div>
//               ) : productsError ? (
//                 <div className={styles.errorContainer}>
//                   <h3>Error Loading Products</h3>
//                   <p>Failed to load products. Please try again.</p>
//                 </div>
//               ) : products.length > 0 ? (
//                 <>
//                   <div className={`${styles.productsGrid} ${styles[viewMode]}`}>
//                     {products.map((product) => (
//                       <ProductCard
//                         key={product.id}
//                         product={product}
//                         viewMode={viewMode}
//                       />
//                     ))}
//                   </div>

//                   {/* Load More */}
//                   {hasNextPage && (
//                     <div className={styles.loadMore}>
//                       <Button
//                         variant="secondary"
//                         size="lg"
//                         onClick={() => fetchNextPage()}
//                         loading={isFetchingNextPage}
//                       >
//                         Load More Products
//                       </Button>
//                     </div>
//                   )}
//                 </>
//               ) : (
//                 <div className={styles.emptyState}>
//                   <div className={styles.emptyIcon}>📦</div>
//                   <h3>No products found</h3>
//                   <p>
//                     There are no products from this brand yet.
//                     Try adjusting your filters or check back later.
//                   </p>
//                   <Button
//                     href="/products"
//                     variant="primary"
//                   >
//                     Browse All Products
//                   </Button>
//                 </div>
//               )}
//             </div>
//           </div>
//         </div>
//       </div>
//     </MainLayout>
//   )
// }
