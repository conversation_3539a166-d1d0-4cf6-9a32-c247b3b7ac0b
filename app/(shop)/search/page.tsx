'use client'

// Search results page
// Displays search results with filters and sorting

import { useState, useEffect, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { 
  FiSearch, 
  FiFilter, 
  FiGrid, 
  FiList,
  FiX,
  FiSliders
} from 'react-icons/fi'
import { MainLayout } from '../../../src/components/layout/MainLayout'
import { ProductCard } from '../../../src/components/features/ProductCard'
import { ProductFilters } from '../../../src/components/features/ProductFilters'
import { Button } from '../../../src/components/ui/Button'
import { Input } from '../../../src/components/ui/Input'
import { LoadingSpinner } from '../../../src/components/ui/LoadingSpinner'
import { useSearchProducts } from '../../../src/hooks/queries/useProducts'
import { useFilterStore, useFilterActions } from '../../../src/stores/filter-store'
import styles from './page.module.scss'

function SearchContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const query = searchParams.get('q') || ''
  
  const [searchQuery, setSearchQuery] = useState(query)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [showFilters, setShowFilters] = useState(false)
  const [sortBy, setSortBy] = useState('relevance')

  const { filters } = useFilterStore()
  const { setSearchTerm } = useFilterActions()

  // Update search term in filter store
  useEffect(() => {
    setSearchTerm(query)
  }, [query, setSearchTerm])

  // Search products with current filters
  const { 
    data: searchResults, 
    isLoading, 
    error,
    refetch 
  } = useSearchProducts({
    search: query,
    ...filters,
    ordering: sortBy === 'relevance' ? undefined : sortBy,
  })

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`)
    }
  }

  const handleClearSearch = () => {
    setSearchQuery('')
    router.push('/search')
  }

  const sortOptions = [
    { value: 'relevance', label: 'Relevance' },
    { value: 'name', label: 'Name A-Z' },
    { value: '-name', label: 'Name Z-A' },
    { value: 'price', label: 'Price: Low to High' },
    { value: '-price', label: 'Price: High to Low' },
    { value: '-created_at', label: 'Newest First' },
    { value: 'created_at', label: 'Oldest First' },
    { value: '-average_rating', label: 'Highest Rated' },
  ]

  const products = searchResults?.results || []
  const totalResults = searchResults?.count || 0
  const hasResults = products.length > 0

  return (
    <MainLayout>
      <div className={styles.searchPage}>
        <div className={styles.container}>
          {/* Search Header */}
          <div className={styles.searchHeader}>
            <form onSubmit={handleSearch} className={styles.searchForm}>
              <Input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search products..."
                leftIcon={<FiSearch size={20} />}
                rightIcon={
                  searchQuery && (
                    <button
                      type="button"
                      onClick={handleClearSearch}
                      className={styles.clearButton}
                    >
                      <FiX size={18} />
                    </button>
                  )
                }
                className={styles.searchInput}
              />
              <Button type="submit" variant="primary">
                Search
              </Button>
            </form>
          </div>

          {/* Results Header */}
          {query && (
            <div className={styles.resultsHeader}>
              <div className={styles.resultsInfo}>
                <h1 className={styles.resultsTitle}>
                  Search results for "{query}"
                </h1>
                {!isLoading && (
                  <p className={styles.resultsCount}>
                    {totalResults} {totalResults === 1 ? 'result' : 'results'} found
                  </p>
                )}
              </div>

              <div className={styles.resultsControls}>
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className={`${styles.filterToggle} ${showFilters ? styles.active : ''}`}
                >
                  <FiSliders size={18} />
                  Filters
                </button>

                <div className={styles.viewControls}>
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`${styles.viewButton} ${viewMode === 'grid' ? styles.active : ''}`}
                  >
                    <FiGrid size={18} />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`${styles.viewButton} ${viewMode === 'list' ? styles.active : ''}`}
                  >
                    <FiList size={18} />
                  </button>
                </div>

                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className={styles.sortSelect}
                >
                  {sortOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          )}

          <div className={styles.searchContent}>
            {/* Filters Sidebar */}
            {showFilters && (
              <div className={styles.filtersSidebar}>
                <div className={styles.filtersHeader}>
                  <h3>Filters</h3>
                  <button
                    onClick={() => setShowFilters(false)}
                    className={styles.closeFilters}
                  >
                    <FiX size={18} />
                  </button>
                </div>
                <ProductFilters />
              </div>
            )}

            {/* Search Results */}
            <div className={styles.searchResults}>
              {isLoading ? (
                <div className={styles.loadingContainer}>
                  <LoadingSpinner size="lg" />
                  <p>Searching products...</p>
                </div>
              ) : error ? (
                <div className={styles.errorContainer}>
                  <h3>Search Error</h3>
                  <p>Failed to search products. Please try again.</p>
                  <Button onClick={() => refetch()} variant="primary">
                    Retry Search
                  </Button>
                </div>
              ) : hasResults ? (
                <div className={`${styles.productsGrid} ${styles[viewMode]}`}>
                  {products.map((product) => (
                    <ProductCard
                      key={product.id}
                      product={product}
                      viewMode={viewMode}
                    />
                  ))}
                </div>
              ) : query ? (
                <div className={styles.noResults}>
                  <div className={styles.noResultsIcon}>🔍</div>
                  <h3>No results found</h3>
                  <p>
                    We couldn't find any products matching "{query}".
                    Try adjusting your search terms or filters.
                  </p>
                  <div className={styles.noResultsActions}>
                    <Button
                      onClick={handleClearSearch}
                      variant="secondary"
                    >
                      Clear Search
                    </Button>
                    <Button
                      href="/products"
                      variant="primary"
                    >
                      Browse All Products
                    </Button>
                  </div>
                </div>
              ) : (
                <div className={styles.searchPrompt}>
                  <div className={styles.searchPromptIcon}>🔍</div>
                  <h3>Search for products</h3>
                  <p>
                    Enter a search term above to find products you're looking for.
                  </p>
                  <Button
                    href="/products"
                    variant="primary"
                  >
                    Browse All Products
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}

export default function SearchPage() {
  return (
    <Suspense fallback={
      <MainLayout>
        <div className={styles.loadingContainer}>
          <LoadingSpinner size="lg" />
        </div>
      </MainLayout>
    }>
      <SearchContent />
    </Suspense>
  )
}
