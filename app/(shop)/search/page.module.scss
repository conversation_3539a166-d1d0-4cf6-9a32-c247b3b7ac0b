@use '../../../src/styles/variables' as *;
@use '../../../src/styles/mixins' as *;

.searchPage {
  min-height: 100vh;
  background: $gray-50;
  padding: $spacing-6 0 $spacing-20;
}

.container {
  @include container;
  max-width: 1400px;
}

.loadingContainer {
  @include flexbox(center, center);
  flex-direction: column;
  gap: $spacing-4;
  min-height: 50vh;
  text-align: center;
  color: $primary-lighter-text-color;
}

// Search Header
.searchHeader {
  margin-bottom: $spacing-8;
  background: white;
  padding: $spacing-6;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow-sm;
}

.searchForm {
  @include flexbox(center, center);
  gap: $spacing-4;
  max-width: 600px;
  margin: 0 auto;

  @include mobile-only {
    flex-direction: column;
    gap: $spacing-3;
  }
}

.searchInput {
  flex: 1;

  @include mobile-only {
    width: 100%;
  }
}

.clearButton {
  @include flexbox(center, center);
  background: none;
  border: none;
  color: $primary-lighter-text-color;
  cursor: pointer;
  padding: $spacing-1;
  border-radius: $border-radius-sm;
  transition: all $transition-fast;

  &:hover {
    color: $primary-dark-text-color;
    background: $gray-100;
  }
}

// Results Header
.resultsHeader {
  @include flexbox(space-between, flex-start);
  gap: $spacing-4;
  margin-bottom: $spacing-6;
  padding: $spacing-4;
  background: white;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow-sm;

  @include mobile-only {
    flex-direction: column;
    gap: $spacing-3;
  }
}

.resultsInfo {
  flex: 1;
}

.resultsTitle {
  font-size: clamp(1.25rem, 4vw, 1.75rem);
  font-weight: 700;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-1;
}

.resultsCount {
  font-size: $font-size-2;
  color: $primary-lighter-text-color;
}

.resultsControls {
  @include flexbox(flex-end, center);
  gap: $spacing-3;
  flex-wrap: wrap;

  @include mobile-only {
    width: 100%;
    justify-content: space-between;
  }
}

.filterToggle {
  @include flexbox(center, center);
  gap: $spacing-2;
  padding: $spacing-2 $spacing-3;
  border: 1px solid $gray-300;
  border-radius: $border-radius-md;
  background: white;
  color: $primary-lighter-text-color;
  font-size: $font-size-2;
  cursor: pointer;
  transition: all $transition-fast;

  &:hover {
    border-color: $primary-blue;
    color: $primary-blue;
  }

  &.active {
    background: $primary-blue;
    border-color: $primary-blue;
    color: white;
  }
}

.viewControls {
  @include flexbox(center, center);
  gap: 0;
  border: 1px solid $gray-300;
  border-radius: $border-radius-md;
  overflow: hidden;
}

.viewButton {
  @include flexbox(center, center);
  width: 40px;
  height: 40px;
  border: none;
  background: white;
  color: $primary-lighter-text-color;
  cursor: pointer;
  transition: all $transition-fast;

  &:hover {
    background: $gray-100;
    color: $primary-dark-text-color;
  }

  &.active {
    background: $primary-blue;
    color: white;
  }
}

.sortSelect {
  min-width: 180px;
  padding: $spacing-2 $spacing-3;
  border: 1px solid $gray-300;
  border-radius: $border-radius-md;
  font-size: $font-size-2;
  color: $primary-dark-text-color;
  background: white;
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: $primary-blue;
    box-shadow: 0 0 0 3px rgba($primary-blue, 0.1);
  }

  @include mobile-only {
    min-width: 150px;
  }
}

// Search Content
.searchContent {
  display: grid;
  grid-template-columns: 1fr;
  gap: $spacing-6;

  &:has(.filtersSidebar) {
    @include tablet-up {
      grid-template-columns: 300px 1fr;
    }
  }
}

.filtersSidebar {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-6;
  box-shadow: $box-shadow-sm;
  height: fit-content;

  @include mobile-only {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: $z-modal;
    border-radius: 0;
    overflow-y: auto;
  }
}

.filtersHeader {
  @include flexbox(space-between, center);
  margin-bottom: $spacing-4;
  padding-bottom: $spacing-3;
  border-bottom: 1px solid $gray-200;

  h3 {
    font-size: $font-size-3;
    font-weight: 600;
    color: $primary-dark-text-color;
  }
}

.closeFilters {
  @include flexbox(center, center);
  width: 32px;
  height: 32px;
  border: none;
  background: $gray-100;
  color: $primary-lighter-text-color;
  border-radius: $border-radius-md;
  cursor: pointer;
  transition: all $transition-fast;

  &:hover {
    background: $gray-200;
    color: $primary-dark-text-color;
  }

  @include tablet-up {
    display: none;
  }
}

// Search Results
.searchResults {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-6;
  box-shadow: $box-shadow-sm;
}

.productsGrid {
  display: grid;
  gap: $spacing-6;

  &.grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));

    @include mobile-only {
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: $spacing-4;
    }
  }

  &.list {
    grid-template-columns: 1fr;
    gap: $spacing-4;
  }
}

// Error State
.errorContainer {
  @include flexbox(center, center);
  flex-direction: column;
  text-align: center;
  padding: $spacing-12 $spacing-4;

  h3 {
    font-size: $font-size-3;
    font-weight: 600;
    color: $error;
    margin-bottom: $spacing-2;
  }

  p {
    font-size: $font-size-2;
    color: $primary-lighter-text-color;
    margin-bottom: $spacing-6;
  }
}

// No Results State
.noResults {
  @include flexbox(center, center);
  flex-direction: column;
  text-align: center;
  padding: $spacing-12 $spacing-4;
}

.noResultsIcon {
  font-size: 4rem;
  margin-bottom: $spacing-4;
  opacity: 0.6;
}

.noResults h3 {
  font-size: $font-size-3;
  font-weight: 600;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-2;
}

.noResults p {
  font-size: $font-size-2;
  color: $primary-lighter-text-color;
  margin-bottom: $spacing-6;
  max-width: 400px;
}

.noResultsActions {
  @include flexbox(center, center);
  gap: $spacing-3;

  @include mobile-only {
    flex-direction: column;
    width: 100%;
    
    > * {
      width: 100%;
    }
  }
}

// Search Prompt State
.searchPrompt {
  @include flexbox(center, center);
  flex-direction: column;
  text-align: center;
  padding: $spacing-12 $spacing-4;
}

.searchPromptIcon {
  font-size: 4rem;
  margin-bottom: $spacing-4;
  opacity: 0.6;
}

.searchPrompt h3 {
  font-size: $font-size-3;
  font-weight: 600;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-2;
}

.searchPrompt p {
  font-size: $font-size-2;
  color: $primary-lighter-text-color;
  margin-bottom: $spacing-6;
  max-width: 400px;
}

// Responsive adjustments
@include mobile-only {
  .searchPage {
    padding: $spacing-4 0 $spacing-16;
  }

  .searchHeader,
  .resultsHeader,
  .searchResults {
    padding: $spacing-4;
  }

  .filtersSidebar {
    padding: $spacing-4;
  }

  .productsGrid.grid {
    grid-template-columns: 1fr;
  }
}
