'use client'

// Shopping cart page
// Displays cart items with management controls and checkout navigation

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { 
  FiTrash2, 
  FiMinus, 
  FiPlus, 
  FiShoppingBag, 
  FiArrowRight,
  FiHeart,
  FiTruck,
  FiShield
} from 'react-icons/fi'
import { MainLayout } from '../../../src/components/layout/MainLayout'
import { Button } from '../../../src/components/ui/Button'
import { useCartStore, useCartActions } from '../../../src/stores/cart-store'
import { useUIActions } from '../../../src/stores/ui-store'
import { formatCurrency } from '../../../src/lib/utils'
import type { CartItem } from '../../../src/types'
import styles from './page.module.scss'

export default function CartPage() {
  const { items, totalItems, totalPrice, subtotal, tax, shipping } = useCartStore()
  const { updateQuantity, removeItem, clearCart } = useCartActions()
  const { addNotification } = useUIActions()
  const [isUpdating, setIsUpdating] = useState<string | null>(null)

  const handleQuantityChange = async (itemId: string, newQuantity: number) => {
    if (newQuantity < 1) return

    setIsUpdating(itemId)
    try {
      updateQuantity(itemId, newQuantity)
      addNotification({
        type: 'success',
        title: 'Cart updated',
        message: 'Item quantity has been updated.',
      })
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Update failed',
        message: 'Failed to update item quantity.',
      })
    } finally {
      setIsUpdating(null)
    }
  }

  const handleRemoveItem = async (item: CartItem) => {
    try {
      removeItem(item.id)
      addNotification({
        type: 'success',
        title: 'Item removed',
        message: `${item.product.name} has been removed from your cart.`,
      })
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Remove failed',
        message: 'Failed to remove item from cart.',
      })
    }
  }

  const handleMoveToWishlist = (item: CartItem) => {
    // TODO: Implement wishlist functionality
    addNotification({
      type: 'info',
      title: 'Wishlist feature',
      message: 'Wishlist functionality will be implemented soon.',
    })
  }

  const handleClearCart = () => {
    clearCart()
    addNotification({
      type: 'success',
      title: 'Cart cleared',
      message: 'All items have been removed from your cart.',
    })
  }

  if (items.length === 0) {
    return (
      <MainLayout>
        <div className={styles.cartPage}>
          <div className={styles.container}>
            <div className={styles.emptyCart}>
              <div className={styles.emptyIcon}>🛒</div>
              <h1 className={styles.emptyTitle}>Your cart is empty</h1>
              <p className={styles.emptyDescription}>
                Looks like you haven't added any items to your cart yet.
                Start shopping to fill it up!
              </p>
              <Button
                variant="primary"
                size="lg"
                leftIcon={<FiShoppingBag size={20} />}
                href="/products"
              >
                Continue Shopping
              </Button>
            </div>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className={styles.cartPage}>
        <div className={styles.container}>
          <div className={styles.header}>
            <h1 className={styles.title}>Shopping Cart</h1>
            <p className={styles.subtitle}>
              {totalItems} {totalItems === 1 ? 'item' : 'items'} in your cart
            </p>
          </div>

          <div className={styles.cartContent}>
            {/* Cart Items */}
            <div className={styles.cartItems}>
              <div className={styles.itemsHeader}>
                <h2 className={styles.itemsTitle}>Items</h2>
                <button
                  onClick={handleClearCart}
                  className={styles.clearButton}
                >
                  Clear Cart
                </button>
              </div>

              <div className={styles.itemsList}>
                {items.map((item) => (
                  <div key={item.id} className={styles.cartItem}>
                    {/* Product Image */}
                    <Link 
                      href={`/products/${item.product.slug}`}
                      className={styles.itemImage}
                    >
                      {item.variant.images?.[0] || item.product.images?.[0] ? (
                        <Image
                          src={
                            item.variant.images?.[0]?.image || 
                            item.product.images?.[0]?.image || 
                            ''
                          }
                          alt={item.product.name}
                          fill
                          className={styles.image}
                        />
                      ) : (
                        <div className={styles.imagePlaceholder}>📦</div>
                      )}
                    </Link>

                    {/* Product Info */}
                    <div className={styles.itemInfo}>
                      <Link 
                        href={`/products/${item.product.slug}`}
                        className={styles.itemName}
                      >
                        {item.product.name}
                      </Link>
                      
                      {item.variant.attributes.length > 0 && (
                        <div className={styles.itemVariant}>
                          {item.variant.attributes.map(attr => attr.value).join(' / ')}
                        </div>
                      )}

                      <div className={styles.itemPrice}>
                        {formatCurrency(item.variant.price)}
                        {item.variant.compare_at_price && (
                          <span className={styles.comparePrice}>
                            {formatCurrency(item.variant.compare_at_price)}
                          </span>
                        )}
                      </div>

                      {/* Mobile Actions */}
                      <div className={styles.mobileActions}>
                        <div className={styles.quantityControls}>
                          <button
                            onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                            disabled={item.quantity <= 1 || isUpdating === item.id}
                            className={styles.quantityButton}
                          >
                            <FiMinus size={16} />
                          </button>
                          <span className={styles.quantity}>{item.quantity}</span>
                          <button
                            onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                            disabled={isUpdating === item.id}
                            className={styles.quantityButton}
                          >
                            <FiPlus size={16} />
                          </button>
                        </div>

                        <div className={styles.itemActions}>
                          <button
                            onClick={() => handleMoveToWishlist(item)}
                            className={styles.actionButton}
                            title="Move to wishlist"
                          >
                            <FiHeart size={18} />
                          </button>
                          <button
                            onClick={() => handleRemoveItem(item)}
                            className={styles.actionButton}
                            title="Remove item"
                          >
                            <FiTrash2 size={18} />
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Desktop Quantity Controls */}
                    <div className={styles.desktopQuantity}>
                      <div className={styles.quantityControls}>
                        <button
                          onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                          disabled={item.quantity <= 1 || isUpdating === item.id}
                          className={styles.quantityButton}
                        >
                          <FiMinus size={16} />
                        </button>
                        <span className={styles.quantity}>{item.quantity}</span>
                        <button
                          onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                          disabled={isUpdating === item.id}
                          className={styles.quantityButton}
                        >
                          <FiPlus size={16} />
                        </button>
                      </div>
                    </div>

                    {/* Desktop Actions */}
                    <div className={styles.desktopActions}>
                      <button
                        onClick={() => handleMoveToWishlist(item)}
                        className={styles.actionButton}
                        title="Move to wishlist"
                      >
                        <FiHeart size={18} />
                      </button>
                      <button
                        onClick={() => handleRemoveItem(item)}
                        className={styles.actionButton}
                        title="Remove item"
                      >
                        <FiTrash2 size={18} />
                      </button>
                    </div>

                    {/* Item Total */}
                    <div className={styles.itemTotal}>
                      {formatCurrency(item.variant.price * item.quantity)}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Cart Summary */}
            <div className={styles.cartSummary}>
              <div className={styles.summaryCard}>
                <h3 className={styles.summaryTitle}>Order Summary</h3>
                
                <div className={styles.summaryDetails}>
                  <div className={styles.summaryRow}>
                    <span>Subtotal ({totalItems} items)</span>
                    <span>{formatCurrency(subtotal)}</span>
                  </div>
                  
                  <div className={styles.summaryRow}>
                    <span>Shipping</span>
                    <span>{shipping > 0 ? formatCurrency(shipping) : 'Free'}</span>
                  </div>
                  
                  <div className={styles.summaryRow}>
                    <span>Tax</span>
                    <span>{formatCurrency(tax)}</span>
                  </div>
                  
                  <div className={styles.summaryDivider} />
                  
                  <div className={styles.summaryTotal}>
                    <span>Total</span>
                    <span>{formatCurrency(totalPrice)}</span>
                  </div>
                </div>

                <Button
                  variant="primary"
                  size="lg"
                  rightIcon={<FiArrowRight size={20} />}
                  href="/checkout"
                  fullWidth
                >
                  Proceed to Checkout
                </Button>

                <div className={styles.securityFeatures}>
                  <div className={styles.feature}>
                    <FiShield size={16} />
                    <span>Secure checkout</span>
                  </div>
                  <div className={styles.feature}>
                    <FiTruck size={16} />
                    <span>Free shipping over $50</span>
                  </div>
                </div>
              </div>

              <div className={styles.continueShoppingCard}>
                <Button
                  variant="secondary"
                  size="md"
                  leftIcon={<FiShoppingBag size={18} />}
                  href="/products"
                  fullWidth
                >
                  Continue Shopping
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
