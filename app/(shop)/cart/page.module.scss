@use '../../../src/styles/variables' as *;
@use '../../../src/styles/mixins' as *;

.cartPage {
  min-height: 100vh;
  background: $gray-50;
  padding: $spacing-6 0 $spacing-20;
}

.container {
  @include container;
}

// Empty Cart State
.emptyCart {
  @include flexbox(center, center);
  flex-direction: column;
  text-align: center;
  padding: $spacing-20 $spacing-4;
  max-width: 500px;
  margin: 0 auto;
}

.emptyIcon {
  font-size: 5rem;
  margin-bottom: $spacing-6;
  opacity: 0.6;
}

.emptyTitle {
  font-size: clamp(1.5rem, 4vw, 2rem);
  font-weight: 700;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-4;
}

.emptyDescription {
  font-size: $font-size-2;
  color: $primary-lighter-text-color;
  line-height: 1.6;
  margin-bottom: $spacing-8;
}

// Cart Header
.header {
  margin-bottom: $spacing-8;
  text-align: center;

  @include tablet-up {
    text-align: left;
  }
}

.title {
  font-size: clamp(1.75rem, 4vw, 2.5rem);
  font-weight: 700;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-2;
}

.subtitle {
  font-size: $font-size-2;
  color: $primary-lighter-text-color;
}

// Cart Content Layout
.cartContent {
  display: grid;
  grid-template-columns: 1fr;
  gap: $spacing-8;

  @include tablet-up {
    grid-template-columns: 2fr 1fr;
    gap: $spacing-12;
  }
}

// Cart Items Section
.cartItems {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-6;
  box-shadow: $box-shadow-sm;
}

.itemsHeader {
  @include flexbox(space-between, center);
  margin-bottom: $spacing-6;
  padding-bottom: $spacing-4;
  border-bottom: 1px solid $gray-200;
}

.itemsTitle {
  font-size: $font-size-4;
  font-weight: 600;
  color: $primary-dark-text-color;
}

.clearButton {
  background: none;
  border: none;
  color: $error;
  font-size: $font-size-1;
  cursor: pointer;
  padding: $spacing-2 $spacing-3;
  border-radius: $border-radius-md;
  transition: background-color $transition-fast;

  &:hover {
    background: $error-bg;
  }
}

.itemsList {
  display: flex;
  flex-direction: column;
  gap: $spacing-6;
}

// Cart Item
.cartItem {
  display: grid;
  grid-template-columns: 80px 1fr;
  gap: $spacing-4;
  padding: $spacing-4;
  border: 1px solid $gray-200;
  border-radius: $border-radius-md;
  transition: border-color $transition-fast;

  &:hover {
    border-color: $primary-blue-light;
  }

  @include tablet-up {
    grid-template-columns: 100px 1fr auto auto auto;
    gap: $spacing-6;
    align-items: center;
  }
}

.itemImage {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: $border-radius-md;
  overflow: hidden;
  background: $gray-100;
  flex-shrink: 0;

  @include tablet-up {
    width: 100px;
    height: 100px;
  }
}

.image {
  object-fit: cover;
  transition: transform $transition-fast;

  &:hover {
    transform: scale(1.05);
  }
}

.imagePlaceholder {
  @include flexbox(center, center);
  width: 100%;
  height: 100%;
  font-size: 2rem;
  color: $gray-400;
}

.itemInfo {
  display: flex;
  flex-direction: column;
  gap: $spacing-1;
  min-width: 0;
}

.itemName {
  font-size: $font-size-2;
  font-weight: 600;
  color: $primary-dark-text-color;
  text-decoration: none;
  line-height: 1.4;
  transition: color $transition-fast;

  &:hover {
    color: $primary-blue;
  }
}

.itemVariant {
  font-size: $font-size-1;
  color: $primary-lighter-text-color;
}

.itemPrice {
  @include flexbox(flex-start, center);
  gap: $spacing-2;
  font-size: $font-size-2;
  font-weight: 600;
  color: $primary-blue;
}

.comparePrice {
  font-size: $font-size-1;
  color: $gray-500;
  text-decoration: line-through;
  font-weight: normal;
}

// Mobile Actions (visible on mobile only)
.mobileActions {
  @include flexbox(space-between, center);
  margin-top: $spacing-3;

  @include tablet-up {
    display: none;
  }
}

// Desktop Controls (visible on tablet+ only)
.desktopQuantity,
.desktopActions {
  display: none;

  @include tablet-up {
    display: flex;
  }
}

.desktopActions {
  @include flexbox(center, center);
  gap: $spacing-2;
}

.itemTotal {
  display: none;
  font-size: $font-size-3;
  font-weight: 700;
  color: $primary-dark-text-color;
  text-align: right;

  @include tablet-up {
    display: block;
  }
}

// Quantity Controls
.quantityControls {
  @include flexbox(center, center);
  gap: 0;
  border: 1px solid $gray-300;
  border-radius: $border-radius-md;
  overflow: hidden;
  background: white;
}

.quantityButton {
  @include flexbox(center, center);
  width: 32px;
  height: 32px;
  border: none;
  background: $gray-100;
  color: $primary-dark-text-color;
  cursor: pointer;
  transition: background-color $transition-fast;

  &:hover:not(:disabled) {
    background: $gray-200;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.quantity {
  @include flexbox(center, center);
  min-width: 40px;
  height: 32px;
  background: white;
  font-weight: 600;
  font-size: $font-size-1;
  border-left: 1px solid $gray-300;
  border-right: 1px solid $gray-300;
}

// Action Buttons
.itemActions {
  @include flexbox(center, center);
  gap: $spacing-2;
}

.actionButton {
  @include flexbox(center, center);
  width: 36px;
  height: 36px;
  border: none;
  background: $gray-100;
  color: $primary-lighter-text-color;
  border-radius: $border-radius-md;
  cursor: pointer;
  transition: all $transition-fast;

  &:hover {
    background: $gray-200;
    color: $primary-dark-text-color;
  }

  &:last-child:hover {
    background: $error-bg;
    color: $error;
  }
}

// Cart Summary
.cartSummary {
  display: flex;
  flex-direction: column;
  gap: $spacing-4;
}

.summaryCard,
.continueShoppingCard {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-6;
  box-shadow: $box-shadow-sm;
}

.summaryTitle {
  font-size: $font-size-3;
  font-weight: 600;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-6;
  text-align: center;
}

.summaryDetails {
  margin-bottom: $spacing-6;
}

.summaryRow {
  @include flexbox(space-between, center);
  padding: $spacing-3 0;
  font-size: $font-size-2;
  color: $primary-lighter-text-color;

  &:last-child {
    padding-bottom: 0;
  }

  span:last-child {
    font-weight: 600;
    color: $primary-dark-text-color;
  }
}

.summaryDivider {
  height: 1px;
  background: $gray-200;
  margin: $spacing-4 0;
}

.summaryTotal {
  @include flexbox(space-between, center);
  padding: $spacing-4 0;
  font-size: $font-size-3;
  font-weight: 700;
  color: $primary-dark-text-color;
  border-top: 2px solid $gray-200;
  margin-top: $spacing-4;

  span:last-child {
    color: $primary-blue;
    font-size: $font-size-4;
  }
}

.securityFeatures {
  display: flex;
  flex-direction: column;
  gap: $spacing-2;
  margin-top: $spacing-6;
  padding-top: $spacing-4;
  border-top: 1px solid $gray-200;
}

.feature {
  @include flexbox(flex-start, center);
  gap: $spacing-2;
  font-size: $font-size-1;
  color: $primary-lighter-text-color;

  svg {
    color: $primary-blue;
    flex-shrink: 0;
  }
}

// Responsive adjustments
@include mobile-only {
  .cartPage {
    padding: $spacing-4 0 $spacing-16;
  }

  .cartItems,
  .summaryCard,
  .continueShoppingCard {
    padding: $spacing-4;
  }

  .cartItem {
    grid-template-columns: 60px 1fr;
    gap: $spacing-3;
    padding: $spacing-3;
  }

  .itemImage {
    width: 60px;
    height: 60px;
  }

  .quantityButton {
    width: 28px;
    height: 28px;
  }

  .quantity {
    min-width: 32px;
    height: 28px;
    font-size: 12px;
  }

  .actionButton {
    width: 32px;
    height: 32px;
  }
}
