'use client'

// Checkout page - multi-step checkout process
// Handles address, payment, and order confirmation

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { 
  FiShoppingCart, 
  FiMapPin, 
  FiCreditCard, 
  FiCheck,
  FiArrowLeft,
  FiArrowRight,
  FiLock
} from 'react-icons/fi'
import { MainLayout } from '../../../src/components/layout/MainLayout'
import { Button } from '../../../src/components/ui/Button'
import { useCartStore } from '../../../src/stores/cart-store'
import { useAuthStore } from '../../../src/stores/auth-store'
import { CheckoutSteps } from './components/CheckoutSteps'
import { CartSummary } from './components/CartSummary'
import { ShippingStep } from './components/ShippingStep'
import { PaymentStep } from './components/PaymentStep'
import { ReviewStep } from './components/ReviewStep'
import { SuccessStep } from './components/SuccessStep'
import styles from './page.module.scss'

export type CheckoutStep = 'shipping' | 'payment' | 'review' | 'success'

export interface CheckoutData {
  shippingAddress: any
  billingAddress: any
  paymentMethod: any
  orderNotes?: string
}

export default function CheckoutPage() {
  const router = useRouter()
  const { items, totalItems, totalPrice } = useCartStore()
  const { user, isAuthenticated } = useAuthStore()
  
  const [currentStep, setCurrentStep] = useState<CheckoutStep>('shipping')
  const [checkoutData, setCheckoutData] = useState<CheckoutData>({
    shippingAddress: null,
    billingAddress: null,
    paymentMethod: null,
  })
  const [isProcessing, setIsProcessing] = useState(false)
  const [orderId, setOrderId] = useState<string | null>(null)

  // Redirect if cart is empty
  useEffect(() => {
    if (items.length === 0 && currentStep !== 'success') {
      router.push('/cart')
    }
  }, [items.length, currentStep, router])

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/checkout')
    }
  }, [isAuthenticated, router])

  const steps: { key: CheckoutStep; title: string; icon: React.ReactNode }[] = [
    { key: 'shipping', title: 'Shipping', icon: <FiMapPin size={20} /> },
    { key: 'payment', title: 'Payment', icon: <FiCreditCard size={20} /> },
    { key: 'review', title: 'Review', icon: <FiShoppingCart size={20} /> },
    { key: 'success', title: 'Complete', icon: <FiCheck size={20} /> },
  ]

  const currentStepIndex = steps.findIndex(step => step.key === currentStep)

  const handleStepComplete = (stepData: any) => {
    setCheckoutData(prev => ({ ...prev, ...stepData }))
    
    // Move to next step
    if (currentStep === 'shipping') {
      setCurrentStep('payment')
    } else if (currentStep === 'payment') {
      setCurrentStep('review')
    } else if (currentStep === 'review') {
      handlePlaceOrder()
    }
  }

  const handlePlaceOrder = async () => {
    setIsProcessing(true)
    try {
      // TODO: Implement order placement API call
      // const order = await OrderService.createOrder(checkoutData)
      
      // Simulate order processing
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Generate mock order ID
      const mockOrderId = `ORD-${Date.now()}`
      setOrderId(mockOrderId)
      setCurrentStep('success')
    } catch (error) {
      console.error('Order placement failed:', error)
      // Handle error - show notification, etc.
    } finally {
      setIsProcessing(false)
    }
  }

  const handleBackStep = () => {
    if (currentStep === 'payment') {
      setCurrentStep('shipping')
    } else if (currentStep === 'review') {
      setCurrentStep('payment')
    }
  }

  const canGoBack = currentStep !== 'shipping' && currentStep !== 'success'

  if (!isAuthenticated || items.length === 0) {
    return null // Will redirect
  }

  return (
    <MainLayout>
      <div className={styles.checkoutPage}>
        <div className={styles.container}>
          {/* Header */}
          <div className={styles.header}>
            <h1 className={styles.title}>Checkout</h1>
            <div className={styles.security}>
              <FiLock size={16} />
              <span>Secure Checkout</span>
            </div>
          </div>

          {/* Progress Steps */}
          <CheckoutSteps 
            steps={steps}
            currentStep={currentStep}
            className={styles.steps}
          />

          <div className={styles.checkoutContent}>
            {/* Main Content */}
            <div className={styles.mainContent}>
              {currentStep === 'shipping' && (
                <ShippingStep
                  initialData={checkoutData.shippingAddress}
                  onComplete={handleStepComplete}
                />
              )}
              
              {currentStep === 'payment' && (
                <PaymentStep
                  initialData={checkoutData.paymentMethod}
                  shippingAddress={checkoutData.shippingAddress}
                  onComplete={handleStepComplete}
                />
              )}
              
              {currentStep === 'review' && (
                <ReviewStep
                  checkoutData={checkoutData}
                  onComplete={handleStepComplete}
                  isProcessing={isProcessing}
                />
              )}
              
              {currentStep === 'success' && orderId && (
                <SuccessStep orderId={orderId} />
              )}

              {/* Navigation */}
              {currentStep !== 'success' && (
                <div className={styles.navigation}>
                  {canGoBack && (
                    <Button
                      variant="secondary"
                      onClick={handleBackStep}
                      leftIcon={<FiArrowLeft size={18} />}
                    >
                      Back
                    </Button>
                  )}
                  
                  <div className={styles.spacer} />
                  
                  {currentStep !== 'review' && (
                    <p className={styles.nextInfo}>
                      Complete this step to continue
                    </p>
                  )}
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className={styles.sidebar}>
              <CartSummary 
                items={items}
                totalPrice={totalPrice}
                currentStep={currentStep}
              />
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
