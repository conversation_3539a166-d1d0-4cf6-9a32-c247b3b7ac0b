'use client'

// Payment step in checkout process
// Handles payment method selection and billing address

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { FiCreditCard, FiDollarSign, FiShield } from 'react-icons/fi'
import { Button } from '../../../../src/components/ui/Button'
import { Input } from '../../../../src/components/ui/Input'
import styles from './PaymentStep.module.scss'

const paymentSchema = z.object({
  paymentMethod: z.enum(['card', 'paypal'], {
    required_error: 'Please select a payment method',
  }),
  cardNumber: z.string().optional(),
  expiryDate: z.string().optional(),
  cvv: z.string().optional(),
  cardholderName: z.string().optional(),
  billingAddressSame: z.boolean().default(true),
  billingAddress: z.object({
    address: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    zipCode: z.string().optional(),
    country: z.string().optional(),
  }).optional(),
}).refine((data) => {
  if (data.paymentMethod === 'card') {
    return data.cardNumber && data.expiryDate && data.cvv && data.cardholderName
  }
  return true
}, {
  message: 'Card details are required for card payment',
  path: ['cardNumber'],
})

type PaymentFormData = z.infer<typeof paymentSchema>

interface PaymentStepProps {
  initialData?: any
  shippingAddress: any
  onComplete: (data: { paymentMethod: PaymentFormData }) => void
}

export function PaymentStep({ initialData, shippingAddress, onComplete }: PaymentStepProps) {
  const [selectedMethod, setSelectedMethod] = useState<'card' | 'paypal'>('card')

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    watch,
    setValue,
  } = useForm<PaymentFormData>({
    resolver: zodResolver(paymentSchema),
    defaultValues: {
      paymentMethod: 'card',
      billingAddressSame: true,
      ...initialData,
    },
    mode: 'onChange',
  })

  const billingAddressSame = watch('billingAddressSame')

  const handleMethodSelect = (method: 'card' | 'paypal') => {
    setSelectedMethod(method)
    setValue('paymentMethod', method)
  }

  const onSubmit = (data: PaymentFormData) => {
    onComplete({ paymentMethod: data })
  }

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '')
    const matches = v.match(/\d{4,16}/g)
    const match = matches && matches[0] || ''
    const parts = []

    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4))
    }

    if (parts.length) {
      return parts.join(' ')
    } else {
      return v
    }
  }

  const formatExpiryDate = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '')
    if (v.length >= 2) {
      return v.substring(0, 2) + '/' + v.substring(2, 4)
    }
    return v
  }

  return (
    <div className={styles.paymentStep}>
      <div className={styles.header}>
        <h2 className={styles.title}>Payment Information</h2>
        <p className={styles.subtitle}>
          Choose your payment method and enter billing details
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className={styles.form}>
        {/* Payment Method Selection */}
        <div className={styles.paymentMethods}>
          <h3 className={styles.sectionTitle}>Payment Method</h3>
          
          <div className={styles.methodOptions}>
            <div
              className={`${styles.methodOption} ${
                selectedMethod === 'card' ? styles.selected : ''
              }`}
              onClick={() => handleMethodSelect('card')}
            >
              <div className={styles.methodIcon}>
                <FiCreditCard size={24} />
              </div>
              <div className={styles.methodInfo}>
                <h4>Credit/Debit Card</h4>
                <p>Visa, Mastercard, American Express</p>
              </div>
            </div>

            <div
              className={`${styles.methodOption} ${
                selectedMethod === 'paypal' ? styles.selected : ''
              }`}
              onClick={() => handleMethodSelect('paypal')}
            >
              <div className={styles.methodIcon}>
                <FiDollarSign size={24} />
              </div>
              <div className={styles.methodInfo}>
                <h4>PayPal</h4>
                <p>Pay with your PayPal account</p>
              </div>
            </div>
          </div>
        </div>

        {/* Card Details */}
        {selectedMethod === 'card' && (
          <div className={styles.cardDetails}>
            <h3 className={styles.sectionTitle}>Card Details</h3>
            
            <Input
              {...register('cardholderName')}
              label="Cardholder Name"
              placeholder="John Doe"
              error={errors.cardholderName?.message}
              fullWidth
            />

            <Input
              {...register('cardNumber')}
              label="Card Number"
              placeholder="1234 5678 9012 3456"
              error={errors.cardNumber?.message}
              onChange={(e) => {
                const formatted = formatCardNumber(e.target.value)
                setValue('cardNumber', formatted)
              }}
              fullWidth
            />

            <div className={styles.row}>
              <Input
                {...register('expiryDate')}
                label="Expiry Date"
                placeholder="MM/YY"
                error={errors.expiryDate?.message}
                onChange={(e) => {
                  const formatted = formatExpiryDate(e.target.value)
                  setValue('expiryDate', formatted)
                }}
                fullWidth
              />
              <Input
                {...register('cvv')}
                label="CVV"
                placeholder="123"
                error={errors.cvv?.message}
                maxLength={4}
                fullWidth
              />
            </div>
          </div>
        )}

        {/* PayPal */}
        {selectedMethod === 'paypal' && (
          <div className={styles.paypalSection}>
            <div className={styles.paypalInfo}>
              <FiShield size={20} />
              <p>You will be redirected to PayPal to complete your payment securely.</p>
            </div>
          </div>
        )}

        {/* Billing Address */}
        <div className={styles.billingAddress}>
          <h3 className={styles.sectionTitle}>Billing Address</h3>
          
          <label className={styles.checkbox}>
            <input
              type="checkbox"
              {...register('billingAddressSame')}
            />
            <span className={styles.checkmark}></span>
            Same as shipping address
          </label>

          {!billingAddressSame && (
            <div className={styles.billingForm}>
              <Input
                {...register('billingAddress.address')}
                label="Address"
                error={errors.billingAddress?.address?.message}
                fullWidth
              />
              
              <div className={styles.row}>
                <Input
                  {...register('billingAddress.city')}
                  label="City"
                  error={errors.billingAddress?.city?.message}
                  fullWidth
                />
                <Input
                  {...register('billingAddress.state')}
                  label="State"
                  error={errors.billingAddress?.state?.message}
                  fullWidth
                />
                <Input
                  {...register('billingAddress.zipCode')}
                  label="ZIP Code"
                  error={errors.billingAddress?.zipCode?.message}
                  fullWidth
                />
              </div>
            </div>
          )}
        </div>

        <Button
          type="submit"
          variant="primary"
          size="lg"
          disabled={!isValid}
          fullWidth
        >
          Continue to Review
        </Button>
      </form>
    </div>
  )
}
