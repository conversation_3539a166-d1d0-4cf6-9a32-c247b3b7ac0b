@use '../../../../src/styles/variables' as *;
@use '../../../../src/styles/mixins' as *;

.shippingStep {
  width: 100%;
}

.header {
  margin-bottom: $spacing-8;
  text-align: center;
}

.title {
  font-size: clamp(1.5rem, 4vw, 2rem);
  font-weight: 700;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-3;
}

.subtitle {
  font-size: $font-size-2;
  color: $primary-lighter-text-color;
  line-height: 1.5;
}

.sectionTitle {
  font-size: $font-size-3;
  font-weight: 600;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-4;
}

// Saved Addresses
.savedAddresses {
  margin-bottom: $spacing-8;
}

.addressList {
  display: grid;
  gap: $spacing-3;
  margin-bottom: $spacing-4;
}

.addressCard {
  border: 2px solid $gray-200;
  border-radius: $border-radius-lg;
  padding: $spacing-4;
  cursor: pointer;
  transition: all $transition-fast;

  &:hover {
    border-color: $primary-blue-light;
  }

  &.selected {
    border-color: $primary-blue;
    background: $sky-lighter-blue;
  }
}

.addressContent {
  width: 100%;
}

.addressHeader {
  @include flexbox(flex-start, center);
  gap: $spacing-2;
  margin-bottom: $spacing-3;
  color: $primary-lighter-text-color;
  font-size: $font-size-1;
}

.addressType {
  font-weight: 600;
}

.selectedIcon {
  margin-left: auto;
  color: $primary-blue;
}

.addressDetails {
  color: $primary-dark-text-color;
  line-height: 1.5;

  p {
    margin-bottom: $spacing-1;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.newAddressButton {
  width: 100%;
}

// New Address Form
.newAddressForm {
  margin-bottom: $spacing-6;
}

.form {
  display: flex;
  flex-direction: column;
  gap: $spacing-4;
}

.row {
  display: grid;
  grid-template-columns: 1fr;
  gap: $spacing-4;

  @include tablet-up {
    grid-template-columns: 1fr 1fr;
  }

  &:has(> :nth-child(3)) {
    @include tablet-up {
      grid-template-columns: 1fr 1fr 1fr;
    }
  }
}

// Continue Section
.continueSection {
  margin-top: $spacing-6;
}

// Responsive adjustments
@include mobile-only {
  .header {
    margin-bottom: $spacing-6;
  }

  .addressCard {
    padding: $spacing-3;
  }

  .row {
    gap: $spacing-3;
  }
}
