@use '../../../../src/styles/variables' as *;
@use '../../../../src/styles/mixins' as *;

.paymentStep {
  width: 100%;
}

.header {
  margin-bottom: $spacing-8;
  text-align: center;
}

.title {
  font-size: clamp(1.5rem, 4vw, 2rem);
  font-weight: 700;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-3;
}

.subtitle {
  font-size: $font-size-2;
  color: $primary-lighter-text-color;
  line-height: 1.5;
}

.form {
  display: flex;
  flex-direction: column;
  gap: $spacing-6;
}

.sectionTitle {
  font-size: $font-size-3;
  font-weight: 600;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-4;
}

// Payment Methods
.paymentMethods {
  margin-bottom: $spacing-6;
}

.methodOptions {
  display: grid;
  gap: $spacing-3;
}

.methodOption {
  @include flexbox(flex-start, center);
  gap: $spacing-4;
  padding: $spacing-4;
  border: 2px solid $gray-200;
  border-radius: $border-radius-lg;
  cursor: pointer;
  transition: all $transition-fast;

  &:hover {
    border-color: $primary-blue-light;
  }

  &.selected {
    border-color: $primary-blue;
    background: $sky-lighter-blue;
  }
}

.methodIcon {
  @include flexbox(center, center);
  width: 48px;
  height: 48px;
  background: $gray-100;
  border-radius: $border-radius-md;
  color: $primary-lighter-text-color;

  .methodOption.selected & {
    background: $primary-blue;
    color: white;
  }
}

.methodInfo {
  flex: 1;

  h4 {
    font-size: $font-size-2;
    font-weight: 600;
    color: $primary-dark-text-color;
    margin-bottom: $spacing-1;
  }

  p {
    font-size: $font-size-1;
    color: $primary-lighter-text-color;
  }
}

// Card Details
.cardDetails {
  margin-bottom: $spacing-6;
}

.row {
  display: grid;
  grid-template-columns: 1fr;
  gap: $spacing-4;

  @include tablet-up {
    grid-template-columns: 1fr 1fr;
  }
}

// PayPal Section
.paypalSection {
  margin-bottom: $spacing-6;
}

.paypalInfo {
  @include flexbox(flex-start, center);
  gap: $spacing-3;
  padding: $spacing-4;
  background: $sky-lighter-blue;
  border-radius: $border-radius-md;
  color: $primary-blue;

  svg {
    flex-shrink: 0;
  }

  p {
    font-size: $font-size-2;
    line-height: 1.5;
  }
}

// Billing Address
.billingAddress {
  margin-bottom: $spacing-6;
}

.checkbox {
  @include flexbox(flex-start, center);
  gap: $spacing-3;
  margin-bottom: $spacing-4;
  cursor: pointer;
  font-size: $font-size-2;
  color: $primary-dark-text-color;

  input[type="checkbox"] {
    display: none;
  }
}

.checkmark {
  position: relative;
  width: 20px;
  height: 20px;
  border: 2px solid $gray-300;
  border-radius: $border-radius-sm;
  background: white;
  transition: all $transition-fast;

  &::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 6px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
    opacity: 0;
    transition: opacity $transition-fast;
  }

  input:checked + & {
    background: $primary-blue;
    border-color: $primary-blue;

    &::after {
      opacity: 1;
    }
  }
}

.billingForm {
  display: flex;
  flex-direction: column;
  gap: $spacing-4;
}

// Responsive adjustments
@include mobile-only {
  .header {
    margin-bottom: $spacing-6;
  }

  .methodOption {
    padding: $spacing-3;
  }

  .methodIcon {
    width: 40px;
    height: 40px;
  }

  .row {
    gap: $spacing-3;
  }
}
