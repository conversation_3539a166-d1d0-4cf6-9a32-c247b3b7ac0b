'use client'

// Success step in checkout process
// Order confirmation and next steps

import { useEffect } from 'react'
import Link from 'next/link'
import { FiCheck, FiMail, FiPackage, FiArrowRight } from 'react-icons/fi'
import { Button } from '../../../../src/components/ui/Button'
import { useCartActions } from '../../../../src/stores/cart-store'
import { useAuthStore } from '../../../../src/stores/auth-store'
import styles from './SuccessStep.module.scss'

interface SuccessStepProps {
  orderId: string
}

export function SuccessStep({ orderId }: SuccessStepProps) {
  const { clearCart } = useCartActions()
  const { user } = useAuthStore()

  // Clear cart on successful order
  useEffect(() => {
    clearCart()
  }, [clearCart])

  return (
    <div className={styles.successStep}>
      <div className={styles.successIcon}>
        <FiCheck size={48} />
      </div>

      <div className={styles.header}>
        <h2 className={styles.title}>Order Placed Successfully!</h2>
        <p className={styles.subtitle}>
          Thank you for your order. We've received your payment and will begin processing your order shortly.
        </p>
      </div>

      <div className={styles.orderInfo}>
        <div className={styles.orderNumber}>
          <h3>Order Number</h3>
          <p className={styles.orderId}>{orderId}</p>
        </div>

        <div className={styles.confirmationEmail}>
          <FiMail size={20} />
          <div>
            <h4>Confirmation Email Sent</h4>
            <p>We've sent a confirmation email to {user?.email}</p>
          </div>
        </div>
      </div>

      <div className={styles.nextSteps}>
        <h3 className={styles.nextStepsTitle}>What's Next?</h3>
        
        <div className={styles.stepsList}>
          <div className={styles.step}>
            <div className={styles.stepIcon}>
              <FiPackage size={20} />
            </div>
            <div className={styles.stepContent}>
              <h4>Order Processing</h4>
              <p>We'll prepare your items for shipment within 1-2 business days.</p>
            </div>
          </div>

          <div className={styles.step}>
            <div className={styles.stepIcon}>
              <FiMail size={20} />
            </div>
            <div className={styles.stepContent}>
              <h4>Shipping Notification</h4>
              <p>You'll receive an email with tracking information once your order ships.</p>
            </div>
          </div>

          <div className={styles.step}>
            <div className={styles.stepIcon}>
              <FiCheck size={20} />
            </div>
            <div className={styles.stepContent}>
              <h4>Delivery</h4>
              <p>Your order will arrive within 3-7 business days depending on your location.</p>
            </div>
          </div>
        </div>
      </div>

      <div className={styles.actions}>
        <Button
          variant="primary"
          size="lg"
          href={`/account/orders/${orderId}`}
          leftIcon={<FiPackage size={20} />}
          fullWidth
        >
          Track Your Order
        </Button>

        <Button
          variant="secondary"
          size="lg"
          href="/products"
          rightIcon={<FiArrowRight size={20} />}
          fullWidth
        >
          Continue Shopping
        </Button>
      </div>

      <div className={styles.support}>
        <h4>Need Help?</h4>
        <p>
          If you have any questions about your order, please{' '}
          <Link href="/contact">contact our support team</Link> or call{' '}
          <a href="tel:**************">**************</a>.
        </p>
      </div>
    </div>
  )
}
