'use client'

// Checkout steps progress indicator
// Shows current step and progress through checkout process

import { FiCheck } from 'react-icons/fi'
import type { CheckoutStep } from '../page'
import styles from './CheckoutSteps.module.scss'

interface CheckoutStepsProps {
  steps: Array<{
    key: CheckoutStep
    title: string
    icon: React.ReactNode
  }>
  currentStep: CheckoutStep
  className?: string
}

export function CheckoutSteps({ steps, currentStep, className = '' }: CheckoutStepsProps) {
  const currentIndex = steps.findIndex(step => step.key === currentStep)

  return (
    <div className={`${styles.checkoutSteps} ${className}`}>
      <div className={styles.stepsContainer}>
        {steps.map((step, index) => {
          const isCompleted = index < currentIndex
          const isCurrent = index === currentIndex
          const isUpcoming = index > currentIndex

          return (
            <div
              key={step.key}
              className={`${styles.step} ${
                isCompleted ? styles.completed : ''
              } ${isCurrent ? styles.current : ''} ${
                isUpcoming ? styles.upcoming : ''
              }`}
            >
              {/* Step Icon */}
              <div className={styles.stepIcon}>
                {isCompleted ? (
                  <FiCheck size={20} />
                ) : (
                  step.icon
                )}
              </div>

              {/* Step Title */}
              <div className={styles.stepContent}>
                <h3 className={styles.stepTitle}>{step.title}</h3>
                <div className={styles.stepStatus}>
                  {isCompleted && 'Completed'}
                  {isCurrent && 'Current'}
                  {isUpcoming && 'Upcoming'}
                </div>
              </div>

              {/* Connector Line */}
              {index < steps.length - 1 && (
                <div className={`${styles.connector} ${
                  isCompleted ? styles.connectorCompleted : ''
                }`} />
              )}
            </div>
          )
        })}
      </div>
    </div>
  )
}
