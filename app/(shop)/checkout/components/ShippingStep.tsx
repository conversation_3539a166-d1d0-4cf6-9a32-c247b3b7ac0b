'use client'

// Shipping step in checkout process
// Handles shipping address selection and entry

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { FiPlus, FiMapPin, FiCheck } from 'react-icons/fi'
import { Button } from '../../../../src/components/ui/Button'
import { Input } from '../../../../src/components/ui/Input'
import { useAuthStore } from '../../../../src/stores/auth-store'
import styles from './ShippingStep.module.scss'

const shippingSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Please enter a valid email'),
  phone: z.string().min(10, 'Please enter a valid phone number'),
  address: z.string().min(1, 'Address is required'),
  apartment: z.string().optional(),
  city: z.string().min(1, 'City is required'),
  state: z.string().min(1, 'State is required'),
  zipCode: z.string().min(5, 'ZIP code is required'),
  country: z.string().min(1, 'Country is required'),
})

type ShippingFormData = z.infer<typeof shippingSchema>

interface ShippingStepProps {
  initialData?: any
  onComplete: (data: { shippingAddress: ShippingFormData }) => void
}

export function ShippingStep({ initialData, onComplete }: ShippingStepProps) {
  const { user } = useAuthStore()
  const [selectedAddress, setSelectedAddress] = useState<string | null>(null)
  const [showNewAddressForm, setShowNewAddressForm] = useState(!user?.addresses?.length)

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    setValue,
    watch,
  } = useForm<ShippingFormData>({
    resolver: zodResolver(shippingSchema),
    defaultValues: {
      firstName: user?.first_name || '',
      lastName: user?.last_name || '',
      email: user?.email || '',
      country: 'United States',
      ...initialData,
    },
    mode: 'onChange',
  })

  const watchedValues = watch()

  const handleAddressSelect = (address: any) => {
    setSelectedAddress(address.id)
    setShowNewAddressForm(false)
    
    // Populate form with selected address
    setValue('firstName', user?.first_name || '')
    setValue('lastName', user?.last_name || '')
    setValue('email', user?.email || '')
    setValue('phone', address.phone || '')
    setValue('address', address.address_line_1)
    setValue('apartment', address.address_line_2 || '')
    setValue('city', address.city)
    setValue('state', address.state)
    setValue('zipCode', address.zip_code)
    setValue('country', address.country)
  }

  const handleNewAddress = () => {
    setSelectedAddress(null)
    setShowNewAddressForm(true)
  }

  const onSubmit = (data: ShippingFormData) => {
    onComplete({ shippingAddress: data })
  }

  return (
    <div className={styles.shippingStep}>
      <div className={styles.header}>
        <h2 className={styles.title}>Shipping Information</h2>
        <p className={styles.subtitle}>
          Choose a shipping address for your order
        </p>
      </div>

      {/* Saved Addresses */}
      {user?.addresses && user.addresses.length > 0 && (
        <div className={styles.savedAddresses}>
          <h3 className={styles.sectionTitle}>Saved Addresses</h3>
          <div className={styles.addressList}>
            {user.addresses.map((address) => (
              <div
                key={address.id}
                className={`${styles.addressCard} ${
                  selectedAddress === address.id ? styles.selected : ''
                }`}
                onClick={() => handleAddressSelect(address)}
              >
                <div className={styles.addressContent}>
                  <div className={styles.addressHeader}>
                    <FiMapPin size={16} />
                    <span className={styles.addressType}>
                      {address.is_default ? 'Default' : 'Saved'} Address
                    </span>
                    {selectedAddress === address.id && (
                      <FiCheck size={16} className={styles.selectedIcon} />
                    )}
                  </div>
                  <div className={styles.addressDetails}>
                    <p>{address.address_line_1}</p>
                    {address.address_line_2 && <p>{address.address_line_2}</p>}
                    <p>{address.city}, {address.state} {address.zip_code}</p>
                    <p>{address.country}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <Button
            variant="secondary"
            onClick={handleNewAddress}
            leftIcon={<FiPlus size={16} />}
            className={styles.newAddressButton}
          >
            Use New Address
          </Button>
        </div>
      )}

      {/* New Address Form */}
      {showNewAddressForm && (
        <div className={styles.newAddressForm}>
          <h3 className={styles.sectionTitle}>
            {user?.addresses?.length ? 'New Address' : 'Shipping Address'}
          </h3>
          
          <form onSubmit={handleSubmit(onSubmit)} className={styles.form}>
            <div className={styles.row}>
              <Input
                {...register('firstName')}
                label="First Name"
                error={errors.firstName?.message}
                fullWidth
              />
              <Input
                {...register('lastName')}
                label="Last Name"
                error={errors.lastName?.message}
                fullWidth
              />
            </div>

            <div className={styles.row}>
              <Input
                {...register('email')}
                type="email"
                label="Email"
                error={errors.email?.message}
                fullWidth
              />
              <Input
                {...register('phone')}
                type="tel"
                label="Phone"
                error={errors.phone?.message}
                fullWidth
              />
            </div>

            <Input
              {...register('address')}
              label="Address"
              error={errors.address?.message}
              fullWidth
            />

            <Input
              {...register('apartment')}
              label="Apartment, suite, etc. (optional)"
              error={errors.apartment?.message}
              fullWidth
            />

            <div className={styles.row}>
              <Input
                {...register('city')}
                label="City"
                error={errors.city?.message}
                fullWidth
              />
              <Input
                {...register('state')}
                label="State"
                error={errors.state?.message}
                fullWidth
              />
              <Input
                {...register('zipCode')}
                label="ZIP Code"
                error={errors.zipCode?.message}
                fullWidth
              />
            </div>

            <Input
              {...register('country')}
              label="Country"
              error={errors.country?.message}
              fullWidth
            />

            <Button
              type="submit"
              variant="primary"
              size="lg"
              disabled={!isValid}
              fullWidth
            >
              Continue to Payment
            </Button>
          </form>
        </div>
      )}

      {/* Continue with Selected Address */}
      {selectedAddress && !showNewAddressForm && (
        <div className={styles.continueSection}>
          <Button
            variant="primary"
            size="lg"
            onClick={() => onSubmit(watchedValues)}
            fullWidth
          >
            Continue to Payment
          </Button>
        </div>
      )}
    </div>
  )
}
