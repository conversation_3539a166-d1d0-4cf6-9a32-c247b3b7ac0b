@use '../../../../src/styles/variables' as *;
@use '../../../../src/styles/mixins' as *;

.cartSummary {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-6;
  box-shadow: $box-shadow-sm;
  height: fit-content;
}

.header {
  @include flexbox(space-between, center);
  margin-bottom: $spacing-6;
  padding-bottom: $spacing-4;
  border-bottom: 1px solid $gray-200;
}

.title {
  font-size: $font-size-3;
  font-weight: 600;
  color: $primary-dark-text-color;
}

.editLink {
  @include flexbox(center, center);
  gap: $spacing-1;
  color: $primary-blue;
  text-decoration: none;
  font-size: $font-size-1;
  padding: $spacing-2 $spacing-3;
  border-radius: $border-radius-md;
  transition: background-color $transition-fast;

  &:hover {
    background: $sky-lighter-blue;
  }
}

// Items
.items {
  margin-bottom: $spacing-6;
}

.item {
  @include flexbox(flex-start, flex-start);
  gap: $spacing-3;
  padding: $spacing-3 0;
  border-bottom: 1px solid $gray-100;

  &:last-child {
    border-bottom: none;
  }
}

.itemImage {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: $border-radius-md;
  overflow: hidden;
  background: $gray-100;
  flex-shrink: 0;
}

.image {
  object-fit: cover;
}

.imagePlaceholder {
  @include flexbox(center, center);
  width: 100%;
  height: 100%;
  font-size: 1.5rem;
  color: $gray-400;
}

.quantity {
  position: absolute;
  top: -6px;
  right: -6px;
  background: $primary-blue;
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: $border-radius-full;
  min-width: 18px;
  text-align: center;
}

.itemInfo {
  flex: 1;
  min-width: 0;
}

.itemName {
  font-size: $font-size-1;
  font-weight: 600;
  color: $primary-dark-text-color;
  line-height: 1.4;
  margin-bottom: $spacing-1;
}

.itemVariant {
  font-size: 11px;
  color: $primary-lighter-text-color;
  margin-bottom: $spacing-1;
}

.itemPrice {
  font-size: $font-size-1;
  font-weight: 600;
  color: $primary-blue;
}

// Breakdown
.breakdown {
  border-top: 1px solid $gray-200;
  padding-top: $spacing-4;
  margin-bottom: $spacing-6;
}

.row {
  @include flexbox(space-between, center);
  padding: $spacing-2 0;
  font-size: $font-size-2;
  color: $primary-lighter-text-color;

  span:last-child {
    font-weight: 600;
    color: $primary-dark-text-color;
  }
}

.divider {
  height: 1px;
  background: $gray-200;
  margin: $spacing-3 0;
}

.total {
  @include flexbox(space-between, center);
  padding: $spacing-3 0;
  font-size: $font-size-3;
  font-weight: 700;
  color: $primary-dark-text-color;
  border-top: 2px solid $gray-200;

  span:last-child {
    color: $primary-blue;
    font-size: $font-size-4;
  }
}

// Security
.security {
  @include flexbox(flex-start, center);
  gap: $spacing-3;
  padding: $spacing-4;
  background: $gray-50;
  border-radius: $border-radius-md;
  margin-top: $spacing-4;
}

.securityIcon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.securityText {
  flex: 1;
}

.securityTitle {
  font-size: $font-size-1;
  font-weight: 600;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-1;
}

.securityDescription {
  font-size: 11px;
  color: $primary-lighter-text-color;
  line-height: 1.4;
}
