@use '../../../../src/styles/variables' as *;
@use '../../../../src/styles/mixins' as *;

.reviewStep {
  width: 100%;
}

.header {
  margin-bottom: $spacing-8;
  text-align: center;
}

.title {
  font-size: clamp(1.5rem, 4vw, 2rem);
  font-weight: 700;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-3;
}

.subtitle {
  font-size: $font-size-2;
  color: $primary-lighter-text-color;
  line-height: 1.5;
}

.reviewSections {
  display: flex;
  flex-direction: column;
  gap: $spacing-6;
  margin-bottom: $spacing-8;
}

.section {
  border: 1px solid $gray-200;
  border-radius: $border-radius-lg;
  overflow: hidden;
}

.sectionHeader {
  @include flexbox(flex-start, center);
  gap: $spacing-3;
  padding: $spacing-4;
  background: $gray-50;
  border-bottom: 1px solid $gray-200;

  svg {
    color: $primary-blue;
    flex-shrink: 0;
  }

  h3 {
    font-size: $font-size-2;
    font-weight: 600;
    color: $primary-dark-text-color;
  }
}

.sectionContent {
  padding: $spacing-4;
  background: white;
}

// Order Items
.orderItems {
  display: flex;
  flex-direction: column;
  gap: $spacing-3;
}

.orderItem {
  @include flexbox(space-between, flex-start);
  gap: $spacing-4;
  padding: $spacing-3;
  background: $gray-50;
  border-radius: $border-radius-md;
}

.itemInfo {
  flex: 1;

  h4 {
    font-size: $font-size-2;
    font-weight: 600;
    color: $primary-dark-text-color;
    margin-bottom: $spacing-1;
  }
}

.itemVariant {
  font-size: $font-size-1;
  color: $primary-lighter-text-color;
  margin-bottom: $spacing-1;
}

.itemQuantity {
  font-size: $font-size-1;
  color: $primary-lighter-text-color;
}

.itemPrice {
  font-size: $font-size-2;
  font-weight: 600;
  color: $primary-blue;
  flex-shrink: 0;
}

// Summary Breakdown
.summaryBreakdown {
  display: flex;
  flex-direction: column;
  gap: $spacing-2;
}

.summaryRow {
  @include flexbox(space-between, center);
  padding: $spacing-2 0;
  font-size: $font-size-2;
  color: $primary-lighter-text-color;

  span:last-child {
    font-weight: 600;
    color: $primary-dark-text-color;
  }
}

.summaryDivider {
  height: 1px;
  background: $gray-200;
  margin: $spacing-3 0;
}

.summaryTotal {
  @include flexbox(space-between, center);
  padding: $spacing-3 0;
  font-size: $font-size-3;
  font-weight: 700;
  color: $primary-dark-text-color;
  border-top: 2px solid $gray-200;

  span:last-child {
    color: $primary-blue;
    font-size: $font-size-4;
  }
}

// Place Order
.placeOrder {
  border-top: 1px solid $gray-200;
  padding-top: $spacing-6;
}

.orderTerms {
  margin-bottom: $spacing-4;
  text-align: center;

  p {
    font-size: $font-size-1;
    color: $primary-lighter-text-color;
    line-height: 1.5;

    a {
      color: $primary-blue;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

// Responsive adjustments
@include mobile-only {
  .header {
    margin-bottom: $spacing-6;
  }

  .sectionHeader,
  .sectionContent {
    padding: $spacing-3;
  }

  .orderItem {
    flex-direction: column;
    gap: $spacing-2;
  }

  .itemPrice {
    align-self: flex-end;
  }
}
