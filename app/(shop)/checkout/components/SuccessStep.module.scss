@use '../../../../src/styles/variables' as *;
@use '../../../../src/styles/mixins' as *;

.successStep {
  @include flexbox(center, center);
  flex-direction: column;
  text-align: center;
  padding: $spacing-8 0;
  max-width: 600px;
  margin: 0 auto;
}

.successIcon {
  @include flexbox(center, center);
  width: 80px;
  height: 80px;
  background: $success;
  color: white;
  border-radius: $border-radius-full;
  margin-bottom: $spacing-6;
  animation: successPulse 2s ease-in-out infinite;
}

@keyframes successPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba($success, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 20px rgba($success, 0);
  }
}

.header {
  margin-bottom: $spacing-8;
}

.title {
  font-size: clamp(1.75rem, 4vw, 2.5rem);
  font-weight: 700;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-4;
}

.subtitle {
  font-size: $font-size-2;
  color: $primary-lighter-text-color;
  line-height: 1.6;
  max-width: 500px;
  margin: 0 auto;
}

.orderInfo {
  @include flexbox(center, center);
  flex-direction: column;
  gap: $spacing-6;
  margin-bottom: $spacing-10;
  padding: $spacing-6;
  background: $gray-50;
  border-radius: $border-radius-lg;
  width: 100%;
}

.orderNumber {
  text-align: center;

  h3 {
    font-size: $font-size-2;
    font-weight: 600;
    color: $primary-lighter-text-color;
    margin-bottom: $spacing-2;
  }
}

.orderId {
  font-size: $font-size-4;
  font-weight: 700;
  color: $primary-blue;
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
}

.confirmationEmail {
  @include flexbox(center, center);
  gap: $spacing-3;
  color: $primary-lighter-text-color;

  svg {
    color: $primary-blue;
    flex-shrink: 0;
  }

  h4 {
    font-size: $font-size-2;
    font-weight: 600;
    color: $primary-dark-text-color;
    margin-bottom: $spacing-1;
  }

  p {
    font-size: $font-size-1;
  }
}

.nextSteps {
  margin-bottom: $spacing-8;
  width: 100%;
}

.nextStepsTitle {
  font-size: $font-size-3;
  font-weight: 600;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-6;
  text-align: center;
}

.stepsList {
  display: flex;
  flex-direction: column;
  gap: $spacing-4;
}

.step {
  @include flexbox(flex-start, center);
  gap: $spacing-4;
  padding: $spacing-4;
  background: white;
  border-radius: $border-radius-lg;
  border: 1px solid $gray-200;
  text-align: left;
}

.stepIcon {
  @include flexbox(center, center);
  width: 40px;
  height: 40px;
  background: $sky-lighter-blue;
  color: $primary-blue;
  border-radius: $border-radius-full;
  flex-shrink: 0;
}

.stepContent {
  flex: 1;

  h4 {
    font-size: $font-size-2;
    font-weight: 600;
    color: $primary-dark-text-color;
    margin-bottom: $spacing-1;
  }

  p {
    font-size: $font-size-1;
    color: $primary-lighter-text-color;
    line-height: 1.5;
  }
}

.actions {
  display: flex;
  flex-direction: column;
  gap: $spacing-3;
  width: 100%;
  margin-bottom: $spacing-8;
}

.support {
  padding: $spacing-4;
  background: $gray-50;
  border-radius: $border-radius-md;
  width: 100%;

  h4 {
    font-size: $font-size-2;
    font-weight: 600;
    color: $primary-dark-text-color;
    margin-bottom: $spacing-2;
  }

  p {
    font-size: $font-size-1;
    color: $primary-lighter-text-color;
    line-height: 1.5;

    a {
      color: $primary-blue;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

// Responsive adjustments
@include mobile-only {
  .successStep {
    padding: $spacing-6 0;
  }

  .successIcon {
    width: 60px;
    height: 60px;
    margin-bottom: $spacing-4;
  }

  .orderInfo {
    padding: $spacing-4;
  }

  .confirmationEmail {
    flex-direction: column;
    text-align: center;
  }

  .step {
    padding: $spacing-3;
  }

  .stepIcon {
    width: 32px;
    height: 32px;
  }
}
