'use client'

// Cart summary for checkout
// Shows order items and pricing breakdown

import Image from 'next/image'
import Link from 'next/link'
import { FiEdit3 } from 'react-icons/fi'
import { formatCurrency } from '../../../../src/lib/utils'
import type { CartItem } from '../../../../src/types'
import type { CheckoutStep } from '../page'
import styles from './CartSummary.module.scss'

interface CartSummaryProps {
  items: CartItem[]
  totalPrice: number
  currentStep: CheckoutStep
}

export function CartSummary({ items, totalPrice, currentStep }: CartSummaryProps) {
  const subtotal = items.reduce((sum, item) => sum + (item.variant.price * item.quantity), 0)
  const shipping = subtotal > 50 ? 0 : 9.99 // Free shipping over $50
  const tax = subtotal * 0.08 // 8% tax rate

  const canEditCart = currentStep === 'shipping'

  return (
    <div className={styles.cartSummary}>
      <div className={styles.header}>
        <h3 className={styles.title}>Order Summary</h3>
        {canEditCart && (
          <Link href="/cart" className={styles.editLink}>
            <FiEdit3 size={16} />
            Edit
          </Link>
        )}
      </div>

      {/* Order Items */}
      <div className={styles.items}>
        {items.map((item) => (
          <div key={item.id} className={styles.item}>
            <div className={styles.itemImage}>
              {item.variant.images?.[0] || item.product.images?.[0] ? (
                <Image
                  src={
                    item.variant.images?.[0]?.image || 
                    item.product.images?.[0]?.image || 
                    ''
                  }
                  alt={item.product.name}
                  fill
                  className={styles.image}
                />
              ) : (
                <div className={styles.imagePlaceholder}>📦</div>
              )}
              <div className={styles.quantity}>{item.quantity}</div>
            </div>

            <div className={styles.itemInfo}>
              <h4 className={styles.itemName}>{item.product.name}</h4>
              {item.variant.attributes.length > 0 && (
                <p className={styles.itemVariant}>
                  {item.variant.attributes.map(attr => attr.value).join(' / ')}
                </p>
              )}
              <p className={styles.itemPrice}>
                {formatCurrency(item.variant.price * item.quantity)}
              </p>
            </div>
          </div>
        ))}
      </div>

      {/* Pricing Breakdown */}
      <div className={styles.breakdown}>
        <div className={styles.row}>
          <span>Subtotal</span>
          <span>{formatCurrency(subtotal)}</span>
        </div>
        
        <div className={styles.row}>
          <span>Shipping</span>
          <span>{shipping > 0 ? formatCurrency(shipping) : 'Free'}</span>
        </div>
        
        <div className={styles.row}>
          <span>Tax</span>
          <span>{formatCurrency(tax)}</span>
        </div>
        
        <div className={styles.divider} />
        
        <div className={styles.total}>
          <span>Total</span>
          <span>{formatCurrency(totalPrice)}</span>
        </div>
      </div>

      {/* Security Badge */}
      <div className={styles.security}>
        <div className={styles.securityIcon}>🔒</div>
        <div className={styles.securityText}>
          <p className={styles.securityTitle}>Secure Checkout</p>
          <p className={styles.securityDescription}>
            Your payment information is encrypted and secure
          </p>
        </div>
      </div>
    </div>
  )
}
