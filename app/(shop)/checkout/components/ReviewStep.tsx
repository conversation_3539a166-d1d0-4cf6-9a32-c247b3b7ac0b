'use client'

// Review step in checkout process
// Final review before placing order

import { FiMapPin, FiCreditCard, FiShoppingCart } from 'react-icons/fi'
import { Button } from '../../../../src/components/ui/Button'
import { formatCurrency } from '../../../../src/lib/utils'
import { useCartStore } from '../../../../src/stores/cart-store'
import type { CheckoutData } from '../page'
import styles from './ReviewStep.module.scss'

interface ReviewStepProps {
  checkoutData: CheckoutData
  onComplete: () => void
  isProcessing: boolean
}

export function ReviewStep({ checkoutData, onComplete, isProcessing }: ReviewStepProps) {
  const { items, totalPrice } = useCartStore()
  const { shippingAddress, paymentMethod } = checkoutData

  const subtotal = items.reduce((sum, item) => sum + (item.variant.price * item.quantity), 0)
  const shipping = subtotal > 50 ? 0 : 9.99
  const tax = subtotal * 0.08

  return (
    <div className={styles.reviewStep}>
      <div className={styles.header}>
        <h2 className={styles.title}>Review Your Order</h2>
        <p className={styles.subtitle}>
          Please review your order details before placing your order
        </p>
      </div>

      <div className={styles.reviewSections}>
        {/* Shipping Address */}
        <div className={styles.section}>
          <div className={styles.sectionHeader}>
            <FiMapPin size={20} />
            <h3>Shipping Address</h3>
          </div>
          <div className={styles.sectionContent}>
            <p>{shippingAddress.firstName} {shippingAddress.lastName}</p>
            <p>{shippingAddress.address}</p>
            {shippingAddress.apartment && <p>{shippingAddress.apartment}</p>}
            <p>{shippingAddress.city}, {shippingAddress.state} {shippingAddress.zipCode}</p>
            <p>{shippingAddress.country}</p>
            <p>{shippingAddress.phone}</p>
          </div>
        </div>

        {/* Payment Method */}
        <div className={styles.section}>
          <div className={styles.sectionHeader}>
            <FiCreditCard size={20} />
            <h3>Payment Method</h3>
          </div>
          <div className={styles.sectionContent}>
            {paymentMethod.paymentMethod === 'card' ? (
              <>
                <p>Credit/Debit Card</p>
                <p>**** **** **** {paymentMethod.cardNumber?.slice(-4)}</p>
                <p>{paymentMethod.cardholderName}</p>
              </>
            ) : (
              <p>PayPal</p>
            )}
          </div>
        </div>

        {/* Order Items */}
        <div className={styles.section}>
          <div className={styles.sectionHeader}>
            <FiShoppingCart size={20} />
            <h3>Order Items ({items.length})</h3>
          </div>
          <div className={styles.sectionContent}>
            <div className={styles.orderItems}>
              {items.map((item) => (
                <div key={item.id} className={styles.orderItem}>
                  <div className={styles.itemInfo}>
                    <h4>{item.product.name}</h4>
                    {item.variant.attributes.length > 0 && (
                      <p className={styles.itemVariant}>
                        {item.variant.attributes.map(attr => attr.value).join(' / ')}
                      </p>
                    )}
                    <p className={styles.itemQuantity}>Qty: {item.quantity}</p>
                  </div>
                  <div className={styles.itemPrice}>
                    {formatCurrency(item.variant.price * item.quantity)}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Order Summary */}
        <div className={styles.section}>
          <div className={styles.sectionHeader}>
            <h3>Order Summary</h3>
          </div>
          <div className={styles.sectionContent}>
            <div className={styles.summaryBreakdown}>
              <div className={styles.summaryRow}>
                <span>Subtotal</span>
                <span>{formatCurrency(subtotal)}</span>
              </div>
              <div className={styles.summaryRow}>
                <span>Shipping</span>
                <span>{shipping > 0 ? formatCurrency(shipping) : 'Free'}</span>
              </div>
              <div className={styles.summaryRow}>
                <span>Tax</span>
                <span>{formatCurrency(tax)}</span>
              </div>
              <div className={styles.summaryDivider} />
              <div className={styles.summaryTotal}>
                <span>Total</span>
                <span>{formatCurrency(totalPrice)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Place Order */}
      <div className={styles.placeOrder}>
        <div className={styles.orderTerms}>
          <p>
            By placing your order, you agree to our{' '}
            <a href="/terms" target="_blank">Terms of Service</a> and{' '}
            <a href="/privacy" target="_blank">Privacy Policy</a>.
          </p>
        </div>

        <Button
          variant="primary"
          size="lg"
          onClick={onComplete}
          loading={isProcessing}
          fullWidth
        >
          {isProcessing ? 'Processing Order...' : 'Place Order'}
        </Button>
      </div>
    </div>
  )
}
