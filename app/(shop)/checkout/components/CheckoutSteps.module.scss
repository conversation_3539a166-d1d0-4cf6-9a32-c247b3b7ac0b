@use '../../../../src/styles/variables' as *;
@use '../../../../src/styles/mixins' as *;

.checkoutSteps {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-6;
  box-shadow: $box-shadow-sm;
}

.stepsContainer {
  @include flexbox(space-between, center);
  position: relative;

  @include mobile-only {
    flex-direction: column;
    gap: $spacing-4;
  }
}

.step {
  @include flexbox(center, center);
  flex-direction: column;
  text-align: center;
  position: relative;
  flex: 1;
  max-width: 200px;

  @include mobile-only {
    flex-direction: row;
    text-align: left;
    max-width: 100%;
    width: 100%;
  }
}

.stepIcon {
  @include flexbox(center, center);
  width: 48px;
  height: 48px;
  border-radius: $border-radius-full;
  margin-bottom: $spacing-3;
  transition: all $transition-fast;
  border: 2px solid $gray-300;
  background: white;
  color: $gray-500;

  @include mobile-only {
    margin-bottom: 0;
    margin-right: $spacing-3;
    width: 40px;
    height: 40px;
  }

  .step.completed & {
    background: $success;
    border-color: $success;
    color: white;
  }

  .step.current & {
    background: $primary-blue;
    border-color: $primary-blue;
    color: white;
  }

  .step.upcoming & {
    background: $gray-100;
    border-color: $gray-300;
    color: $gray-500;
  }
}

.stepContent {
  @include mobile-only {
    flex: 1;
  }
}

.stepTitle {
  font-size: $font-size-2;
  font-weight: 600;
  margin-bottom: $spacing-1;
  transition: color $transition-fast;

  .step.completed & {
    color: $success;
  }

  .step.current & {
    color: $primary-blue;
  }

  .step.upcoming & {
    color: $gray-500;
  }
}

.stepStatus {
  font-size: $font-size-1;
  color: $primary-lighter-text-color;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.connector {
  position: absolute;
  top: 24px;
  left: calc(50% + 24px);
  right: calc(-50% + 24px);
  height: 2px;
  background: $gray-300;
  z-index: 1;
  transition: background-color $transition-fast;

  &.connectorCompleted {
    background: $success;
  }

  @include mobile-only {
    display: none;
  }
}

// Mobile connector
@include mobile-only {
  .step:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 20px;
    top: 48px;
    width: 2px;
    height: 24px;
    background: $gray-300;
  }

  .step.completed:not(:last-child)::after {
    background: $success;
  }
}
