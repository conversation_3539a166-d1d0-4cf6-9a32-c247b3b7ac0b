@use '../../../src/styles/variables' as *;
@use '../../../src/styles/mixins' as *;

.checkoutPage {
  min-height: 100vh;
  background: $gray-50;
  padding: $spacing-6 0 $spacing-20;
}

.container {
  @include container;
  max-width: 1200px;
}

// Header
.header {
  @include flexbox(space-between, center);
  margin-bottom: $spacing-8;
  padding-bottom: $spacing-4;
  border-bottom: 1px solid $gray-200;

  @include mobile-only {
    flex-direction: column;
    gap: $spacing-3;
    text-align: center;
  }
}

.title {
  font-size: clamp(1.75rem, 4vw, 2.5rem);
  font-weight: 700;
  color: $primary-dark-text-color;
}

.security {
  @include flexbox(center, center);
  gap: $spacing-2;
  color: $primary-lighter-text-color;
  font-size: $font-size-1;

  svg {
    color: $success;
  }
}

// Steps
.steps {
  margin-bottom: $spacing-10;
}

// Content Layout
.checkoutContent {
  display: grid;
  grid-template-columns: 1fr;
  gap: $spacing-8;

  @include tablet-up {
    grid-template-columns: 2fr 1fr;
    gap: $spacing-12;
  }
}

.mainContent {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-8;
  box-shadow: $box-shadow-sm;
  height: fit-content;

  @include mobile-only {
    padding: $spacing-6;
  }
}

.sidebar {
  height: fit-content;

  @include mobile-only {
    order: -1;
  }
}

// Navigation
.navigation {
  @include flexbox(space-between, center);
  margin-top: $spacing-8;
  padding-top: $spacing-6;
  border-top: 1px solid $gray-200;
}

.spacer {
  flex: 1;
}

.nextInfo {
  font-size: $font-size-1;
  color: $primary-lighter-text-color;
  text-align: right;

  @include mobile-only {
    text-align: center;
    margin-top: $spacing-2;
  }
}

// Responsive adjustments
@include mobile-only {
  .checkoutPage {
    padding: $spacing-4 0 $spacing-16;
  }

  .navigation {
    flex-direction: column;
    gap: $spacing-4;
  }
}
