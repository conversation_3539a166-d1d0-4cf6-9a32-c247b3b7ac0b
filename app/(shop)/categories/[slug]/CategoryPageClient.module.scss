@use '../../../../src/styles/variables' as *;
@use '../../../../src/styles/mixins' as *;

.categoryPage {
  min-height: 100vh;
  background: $gray-50;
  padding: $spacing-6 0 $spacing-20;
}

.container {
  @include container;
  max-width: 1400px;
}

.loadingContainer {
  @include flexbox(center, center);
  flex-direction: column;
  gap: $spacing-4;
  min-height: 50vh;
  text-align: center;
  color: $primary-lighter-text-color;
}

// Breadcrumb
.breadcrumb {
  @include flexbox(flex-start, center);
  gap: $spacing-2;
  margin-bottom: $spacing-8;
  font-size: $font-size-1;
  color: $primary-lighter-text-color;

  a {
    color: $primary-blue;
    text-decoration: none;
    transition: color $transition-fast;

    &:hover {
      color: $primary-blue-dark;
    }
  }

  span {
    color: $primary-dark-text-color;
    font-weight: 500;
  }

  svg {
    color: $gray-400;
  }
}

// Category Header
.categoryHeader {
  @include flexbox(flex-start, center);
  gap: $spacing-6;
  margin-bottom: $spacing-8;
  padding: $spacing-6;
  background: white;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow-sm;

  @include mobile-only {
    flex-direction: column;
    text-align: center;
    gap: $spacing-4;
    padding: $spacing-4;
  }
}

.categoryImage {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: $border-radius-lg;
  overflow: hidden;
  background: $gray-100;
  flex-shrink: 0;

  @include mobile-only {
    width: 100px;
    height: 100px;
  }
}

.image {
  object-fit: cover;
}

.categoryInfo {
  flex: 1;
}

.categoryTitle {
  font-size: clamp(1.75rem, 4vw, 2.5rem);
  font-weight: 700;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-3;
}

.categoryDescription {
  font-size: $font-size-2;
  color: $primary-lighter-text-color;
  line-height: 1.6;
  margin-bottom: $spacing-3;
  max-width: 600px;
}

.productCount {
  font-size: $font-size-2;
  color: $primary-blue;
  font-weight: 600;
}

// Controls
.controls {
  @include flexbox(space-between, center);
  gap: $spacing-4;
  margin-bottom: $spacing-6;
  padding: $spacing-4;
  background: white;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow-sm;

  @include mobile-only {
    flex-direction: column;
    gap: $spacing-3;
  }
}

.controlsLeft,
.controlsRight {
  @include flexbox(center, center);
  gap: $spacing-3;
}

.controlsRight {
  @include mobile-only {
    width: 100%;
    justify-content: space-between;
  }
}

.filterToggle {
  @include flexbox(center, center);
  gap: $spacing-2;
  padding: $spacing-2 $spacing-3;
  border: 1px solid $gray-300;
  border-radius: $border-radius-md;
  background: white;
  color: $primary-lighter-text-color;
  font-size: $font-size-2;
  cursor: pointer;
  transition: all $transition-fast;

  &:hover {
    border-color: $primary-blue;
    color: $primary-blue;
  }

  &.active {
    background: $primary-blue;
    border-color: $primary-blue;
    color: white;
  }
}

.viewControls {
  @include flexbox(center, center);
  gap: 0;
  border: 1px solid $gray-300;
  border-radius: $border-radius-md;
  overflow: hidden;
}

.viewButton {
  @include flexbox(center, center);
  width: 40px;
  height: 40px;
  border: none;
  background: white;
  color: $primary-lighter-text-color;
  cursor: pointer;
  transition: all $transition-fast;

  &:hover {
    background: $gray-100;
    color: $primary-dark-text-color;
  }

  &.active {
    background: $primary-blue;
    color: white;
  }
}

.sortSelect {
  min-width: 180px;
  padding: $spacing-2 $spacing-3;
  border: 1px solid $gray-300;
  border-radius: $border-radius-md;
  font-size: $font-size-2;
  color: $primary-dark-text-color;
  background: white;
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: $primary-blue;
    box-shadow: 0 0 0 3px rgba($primary-blue, 0.1);
  }

  @include mobile-only {
    min-width: 150px;
  }
}

// Content
.categoryContent {
  display: grid;
  grid-template-columns: 1fr;
  gap: $spacing-6;

  &:has(.filtersSidebar) {
    @include tablet-up {
      grid-template-columns: 300px 1fr;
    }
  }
}

.filtersSidebar {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-6;
  box-shadow: $box-shadow-sm;
  height: fit-content;

  @include mobile-only {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: $z-modal;
    border-radius: 0;
    overflow-y: auto;
  }
}

.filtersHeader {
  @include flexbox(space-between, center);
  margin-bottom: $spacing-4;
  padding-bottom: $spacing-3;
  border-bottom: 1px solid $gray-200;

  h3 {
    font-size: $font-size-3;
    font-weight: 600;
    color: $primary-dark-text-color;
  }
}

.closeFilters {
  @include flexbox(center, center);
  width: 32px;
  height: 32px;
  border: none;
  background: $gray-100;
  color: $primary-lighter-text-color;
  border-radius: $border-radius-md;
  cursor: pointer;
  transition: all $transition-fast;

  &:hover {
    background: $gray-200;
    color: $primary-dark-text-color;
  }

  @include tablet-up {
    display: none;
  }
}

// Products Section
.productsSection {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-6;
  box-shadow: $box-shadow-sm;
}

.productsGrid {
  display: grid;
  gap: $spacing-6;
  margin-bottom: $spacing-8;

  &.grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));

    @include mobile-only {
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: $spacing-4;
    }
  }

  &.list {
    grid-template-columns: 1fr;
    gap: $spacing-4;
  }
}

.loadMore {
  @include flexbox(center, center);
  margin-top: $spacing-6;
}

// Error State
.errorContainer {
  @include flexbox(center, center);
  flex-direction: column;
  text-align: center;
  padding: $spacing-12 $spacing-4;

  h3 {
    font-size: $font-size-3;
    font-weight: 600;
    color: $error;
    margin-bottom: $spacing-2;
  }

  p {
    font-size: $font-size-2;
    color: $primary-lighter-text-color;
    margin-bottom: $spacing-6;
  }
}

// Empty State
.emptyState {
  @include flexbox(center, center);
  flex-direction: column;
  text-align: center;
  padding: $spacing-12 $spacing-4;
}

.emptyIcon {
  font-size: 4rem;
  margin-bottom: $spacing-4;
  opacity: 0.6;
}

.emptyState h3 {
  font-size: $font-size-3;
  font-weight: 600;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-2;
}

.emptyState p {
  font-size: $font-size-2;
  color: $primary-lighter-text-color;
  margin-bottom: $spacing-6;
  max-width: 400px;
}

// Responsive adjustments
@include mobile-only {
  .categoryPage {
    padding: $spacing-4 0 $spacing-16;
  }

  .categoryHeader,
  .controls,
  .productsSection {
    padding: $spacing-4;
  }

  .filtersSidebar {
    padding: $spacing-4;
  }

  .productsGrid.grid {
    grid-template-columns: 1fr;
  }
}
