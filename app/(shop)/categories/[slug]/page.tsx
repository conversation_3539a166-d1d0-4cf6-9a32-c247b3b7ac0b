import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { CategoryPageClient } from './CategoryPageClient'

// This would typically fetch from your API
async function getCategory(slug: string) {
  // For now, return null to trigger client-side fetching
  // In a real app, you'd fetch the category here for SSR
  return null
}

interface CategoryPageProps {
  params: {
    slug: string
  }
}

export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const category = await getCategory(params.slug)
  
  if (!category) {
    return {
      title: 'Category | Picky Store',
      description: 'Browse products by category',
    }
  }

  return {
    title: `${category.name} | Picky Store`,
    description: category.description || `Shop ${category.name} products at Picky Store`,
    openGraph: {
      title: category.name,
      description: category.description || `Shop ${category.name} products`,
      images: category.image ? [category.image] : [],
    },
  }
}

export default function CategoryPage({ params }: CategoryPageProps) {
  return <CategoryPageClient slug={params.slug} />
}
