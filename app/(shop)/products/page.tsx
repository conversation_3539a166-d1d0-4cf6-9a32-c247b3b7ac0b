// Products listing page with filters and pagination
// Server-side rendered for SEO with client-side interactivity

import { Metadata } from 'next';
import { ProductsClient } from './ProductsClient';

export const metadata: Metadata = {
  title: 'Products - Picky Store',
  description: 'Browse our extensive collection of quality products. Find exactly what you\'re looking for with our advanced filtering and search options.',
  keywords: 'products, shopping, e-commerce, online store, browse products',
  openGraph: {
    title: 'Products - Picky Store',
    description: 'Browse our extensive collection of quality products.',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Products - Picky Store',
    description: 'Browse our extensive collection of quality products.',
  },
};

export default function ProductsPage() {
  return <ProductsClient />;
}
