import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { ProductDetailClient } from './ProductDetailClient'

// This would typically fetch from your API
async function getProduct(slug: string) {
  // For now, return null to trigger client-side fetching
  // In a real app, you'd fetch the product here for SSR
  return null
}

interface ProductPageProps {
  params: {
    slug: string
  }
}

export async function generateMetadata({ params }: ProductPageProps): Promise<Metadata> {
  const product = await getProduct(params.slug)
  
  if (!product) {
    return {
      title: 'Product | Picky Store',
      description: 'Product details and information',
    }
  }

  return {
    title: `${product.name} | Picky Store`,
    description: product.short_description || product.description,
    openGraph: {
      title: product.name,
      description: product.short_description || product.description,
      images: product.images?.length > 0 ? [product.images[0].image] : [],
    },
  }
}

export default function ProductPage({ params }: ProductPageProps) {
  return <ProductDetailClient slug={params.slug} />
}
