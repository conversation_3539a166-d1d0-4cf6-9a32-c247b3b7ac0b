@use '../../../../src/styles/variables' as *;
@use '../../../../src/styles/mixins' as *;

.productDetail {
  min-height: 100vh;
  background: white;
}

.container {
  @include container;
  padding-top: $spacing-6;
  padding-bottom: $spacing-20;
}

.loadingContainer {
  @include flexbox(center, center);
  min-height: 50vh;
}

// Breadcrumb
.breadcrumb {
  @include flexbox(flex-start, center);
  gap: $spacing-2;
  margin-bottom: $spacing-8;
  font-size: $font-size-1;
  color: $primary-lighter-text-color;

  a {
    color: $primary-blue;
    text-decoration: none;
    transition: color $transition-fast;

    &:hover {
      color: $primary-blue-dark;
    }
  }

  span {
    color: $gray-400;
  }
}

// Product Content Layout
.productContent {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: $spacing-12;
  margin-bottom: $spacing-16;

  @include mobile-only {
    grid-template-columns: 1fr;
    gap: $spacing-8;
  }
}

// Image Section
.imageSection {
  display: flex;
  flex-direction: column;
  gap: $spacing-4;
}

.mainImage {
  position: relative;
  width: 100%;
  height: 500px;
  border-radius: $border-radius-lg;
  overflow: hidden;
  background: $gray-100;

  @include mobile-only {
    height: 300px;
  }
}

.image {
  object-fit: cover;
  transition: transform $transition-normal;

  &:hover {
    transform: scale(1.05);
  }
}

.imagePlaceholder {
  @include flexbox(center, center);
  width: 100%;
  height: 100%;
  font-size: 4rem;
  color: $gray-400;
  background: $gray-100;
}

.imageNav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  @include flexbox(center, center);
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: $border-radius-full;
  color: $primary-dark-text-color;
  cursor: pointer;
  transition: all $transition-fast;
  z-index: 2;

  &:hover {
    background: white;
    box-shadow: $box-shadow-md;
  }

  &.prevButton {
    left: $spacing-4;
  }

  &.nextButton {
    right: $spacing-4;
  }
}

.thumbnails {
  display: flex;
  gap: $spacing-2;
  overflow-x: auto;
  padding: $spacing-2 0;
}

.thumbnail {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: $border-radius-md;
  overflow: hidden;
  border: 2px solid transparent;
  cursor: pointer;
  transition: border-color $transition-fast;
  flex-shrink: 0;

  &.active {
    border-color: $primary-blue;
  }

  &:hover {
    border-color: $primary-blue-light;
  }
}

.thumbnailImage {
  object-fit: cover;
}

// Product Info
.productInfo {
  display: flex;
  flex-direction: column;
  gap: $spacing-6;
}

.header {
  border-bottom: 1px solid $gray-200;
  padding-bottom: $spacing-6;
}

.title {
  font-size: clamp(1.5rem, 4vw, 2.25rem);
  font-weight: 700;
  color: $primary-dark-text-color;
  line-height: 1.2;
  margin-bottom: $spacing-4;
}

.rating {
  @include flexbox(flex-start, center);
  gap: $spacing-2;
  margin-bottom: $spacing-4;
}

.stars {
  @include flexbox(flex-start, center);
  gap: 2px;
}

.starFilled {
  color: $primary-yellow;
  fill: currentColor;
}

.starEmpty {
  color: $gray-300;
}

.ratingText {
  font-size: $font-size-1;
  color: $primary-lighter-text-color;
}

.priceSection {
  @include flexbox(flex-start, center);
  gap: $spacing-3;
}

.price {
  font-size: clamp(1.5rem, 4vw, 2rem);
  font-weight: 700;
  color: $primary-blue;
}

.comparePrice {
  font-size: $font-size-3;
  color: $gray-500;
  text-decoration: line-through;
}

.shortDescription {
  font-size: $font-size-3;
  color: $primary-lighter-text-color;
  line-height: 1.6;
}

// Variant Selection
.variantSection {
  border-bottom: 1px solid $gray-200;
  padding-bottom: $spacing-6;
}

.variantTitle {
  font-size: $font-size-2;
  font-weight: 600;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-3;
}

.variants {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-2;
}

.variantButton {
  position: relative;
  padding: $spacing-2 $spacing-4;
  border: 2px solid $gray-300;
  border-radius: $border-radius-md;
  background: white;
  color: $primary-dark-text-color;
  font-size: $font-size-1;
  cursor: pointer;
  transition: all $transition-fast;

  &:hover:not(:disabled) {
    border-color: $primary-blue;
  }

  &.active {
    border-color: $primary-blue;
    background: $sky-lighter-blue;
    color: $primary-blue;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.outOfStock {
  position: absolute;
  top: -8px;
  right: -8px;
  background: $error;
  color: white;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: $border-radius-sm;
}

// Purchase Section
.purchaseSection {
  border-bottom: 1px solid $gray-200;
  padding-bottom: $spacing-6;
}

.quantitySection {
  margin-bottom: $spacing-4;
}

.quantityLabel {
  display: block;
  font-size: $font-size-2;
  font-weight: 600;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-2;
}

.quantityControls {
  @include flexbox(flex-start, center);
  gap: 0;
  border: 1px solid $gray-300;
  border-radius: $border-radius-md;
  overflow: hidden;
  width: fit-content;
}

.quantityButton {
  @include flexbox(center, center);
  width: 40px;
  height: 40px;
  border: none;
  background: $gray-100;
  color: $primary-dark-text-color;
  cursor: pointer;
  transition: background-color $transition-fast;

  &:hover:not(:disabled) {
    background: $gray-200;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.quantity {
  @include flexbox(center, center);
  width: 60px;
  height: 40px;
  background: white;
  font-weight: 600;
  border-left: 1px solid $gray-300;
  border-right: 1px solid $gray-300;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: $spacing-3;
}

// Features
.features {
  display: flex;
  flex-direction: column;
  gap: $spacing-3;
}

.feature {
  @include flexbox(flex-start, center);
  gap: $spacing-3;
  color: $primary-lighter-text-color;
  font-size: $font-size-1;

  svg {
    color: $primary-blue;
    flex-shrink: 0;
  }
}

// Description Section
.descriptionSection {
  border-top: 1px solid $gray-200;
  padding-top: $spacing-8;
}

.sectionTitle {
  font-size: $font-size-4;
  font-weight: 700;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-6;
}

.description {
  font-size: $font-size-2;
  color: $primary-lighter-text-color;
  line-height: 1.7;

  h1, h2, h3, h4, h5, h6 {
    color: $primary-dark-text-color;
    margin: $spacing-4 0 $spacing-2;
  }

  p {
    margin-bottom: $spacing-4;
  }

  ul, ol {
    margin: $spacing-4 0;
    padding-left: $spacing-6;
  }

  li {
    margin-bottom: $spacing-1;
  }
}

// Responsive adjustments
@include tablet-up {
  .actions {
    flex-direction: row;
  }
}

@include mobile-only {
  .variantButton {
    flex: 1;
    min-width: 120px;
  }

  .quantityControls {
    width: 100%;
  }

  .quantity {
    flex: 1;
  }
}
