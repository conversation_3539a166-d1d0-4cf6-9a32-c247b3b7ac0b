'use client'

// Product detail client component
// Handles product display, variant selection, and add to cart

import { useState } from 'react'
import { notFound } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import { 
  FiShoppingCart, 
  FiHeart, 
  FiStar, 
  FiChevronLeft,
  FiChevronRight,
  FiMinus,
  FiPlus,
  FiTruck,
  FiShield,
  FiRefreshCw
} from 'react-icons/fi'
import { MainLayout } from '../../../../src/components/layout/MainLayout'
import { Button } from '../../../../src/components/ui/Button'
import { LoadingSpinner } from '../../../../src/components/ui/LoadingSpinner'
import { useProduct } from '../../../../src/hooks/queries/useProducts'
import { useCartActions } from '../../../../src/stores/cart-store'
import { useWishlistActions, useWishlistItem } from '../../../../src/stores/wishlist-store'
import { useUIActions } from '../../../../src/stores/ui-store'
import type { ProductVariant } from '../../../../src/types'
import styles from './ProductDetailClient.module.scss'

interface ProductDetailClientProps {
  slug: string
}

export function ProductDetailClient({ slug }: ProductDetailClientProps) {
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null)
  const [selectedImageIndex, setSelectedImageIndex] = useState(0)
  const [quantity, setQuantity] = useState(1)
  const [isAddingToCart, setIsAddingToCart] = useState(false)

  const { data: product, isLoading, error } = useProduct(slug)
  const { addItem } = useCartActions()
  const { addItem: addToWishlist, removeItem: removeFromWishlist } = useWishlistActions()
  const { isInWishlist } = useWishlistItem(product?.id || '')
  const { addNotification } = useUIActions()

  if (isLoading) {
    return (
      <MainLayout>
        <div className={styles.loadingContainer}>
          <LoadingSpinner size="lg" />
        </div>
      </MainLayout>
    )
  }

  if (error || !product) {
    notFound()
  }

  // Set default variant if not selected
  if (!selectedVariant && product.variants.length > 0) {
    setSelectedVariant(product.variants[0])
  }

  const currentVariant = selectedVariant || product.variants[0]
  const currentImages = currentVariant?.images?.length > 0 ? currentVariant.images : product.images
  const currentImage = currentImages[selectedImageIndex]

  const handleAddToCart = async () => {
    if (!currentVariant) return

    setIsAddingToCart(true)
    try {
      addItem(product, currentVariant, quantity)
      addNotification({
        type: 'success',
        title: 'Added to cart',
        message: `${product.name} has been added to your cart.`,
      })
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Failed to add to cart',
        message: 'Please try again.',
      })
    } finally {
      setIsAddingToCart(false)
    }
  }

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity >= 1 && newQuantity <= (currentVariant?.inventory_quantity || 0)) {
      setQuantity(newQuantity)
    }
  }

  const handleToggleWishlist = () => {
    if (!product) return

    if (isInWishlist) {
      removeFromWishlist(product.id)
      addNotification({
        type: 'success',
        title: 'Removed from wishlist',
        message: `${product.name} has been removed from your wishlist.`,
      })
    } else {
      addToWishlist(product)
      addNotification({
        type: 'success',
        title: 'Added to wishlist',
        message: `${product.name} has been added to your wishlist.`,
      })
    }
  }

  const nextImage = () => {
    setSelectedImageIndex((prev) => 
      prev === currentImages.length - 1 ? 0 : prev + 1
    )
  }

  const prevImage = () => {
    setSelectedImageIndex((prev) => 
      prev === 0 ? currentImages.length - 1 : prev - 1
    )
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <FiStar
        key={i}
        className={i < Math.floor(rating) ? styles.starFilled : styles.starEmpty}
        size={16}
      />
    ))
  }

  return (
    <MainLayout>
      <div className={styles.productDetail}>
        <div className={styles.container}>
          {/* Breadcrumb */}
          <nav className={styles.breadcrumb}>
            <Link href="/">Home</Link>
            <span>/</span>
            <Link href="/products">Products</Link>
            <span>/</span>
            <Link href={`/categories/${product.category.slug}`}>
              {product.category.name}
            </Link>
            <span>/</span>
            <span>{product.name}</span>
          </nav>

          <div className={styles.productContent}>
            {/* Product Images */}
            <div className={styles.imageSection}>
              <div className={styles.mainImage}>
                {currentImage ? (
                  <Image
                    src={currentImage.image}
                    alt={currentImage.alt_text || product.name}
                    fill
                    className={styles.image}
                    priority
                  />
                ) : (
                  <div className={styles.imagePlaceholder}>
                    📷
                  </div>
                )}
                
                {currentImages.length > 1 && (
                  <>
                    <button
                      onClick={prevImage}
                      className={`${styles.imageNav} ${styles.prevButton}`}
                      aria-label="Previous image"
                    >
                      <FiChevronLeft size={24} />
                    </button>
                    <button
                      onClick={nextImage}
                      className={`${styles.imageNav} ${styles.nextButton}`}
                      aria-label="Next image"
                    >
                      <FiChevronRight size={24} />
                    </button>
                  </>
                )}
              </div>

              {/* Image Thumbnails */}
              {currentImages.length > 1 && (
                <div className={styles.thumbnails}>
                  {currentImages.map((image, index) => (
                    <button
                      key={image.id}
                      onClick={() => setSelectedImageIndex(index)}
                      className={`${styles.thumbnail} ${
                        index === selectedImageIndex ? styles.active : ''
                      }`}
                    >
                      <Image
                        src={image.image}
                        alt={image.alt_text || `${product.name} ${index + 1}`}
                        fill
                        className={styles.thumbnailImage}
                      />
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Product Info */}
            <div className={styles.productInfo}>
              <div className={styles.header}>
                <h1 className={styles.title}>{product.name}</h1>
                
                {/* Rating */}
                {product.average_rating && (
                  <div className={styles.rating}>
                    <div className={styles.stars}>
                      {renderStars(product.average_rating)}
                    </div>
                    <span className={styles.ratingText}>
                      {product.average_rating.toFixed(1)} ({product.review_count} reviews)
                    </span>
                  </div>
                )}

                {/* Price */}
                <div className={styles.priceSection}>
                  <span className={styles.price}>
                    ${currentVariant?.price || product.min_price}
                  </span>
                  {currentVariant?.compare_at_price && (
                    <span className={styles.comparePrice}>
                      ${currentVariant.compare_at_price}
                    </span>
                  )}
                </div>
              </div>

              {/* Short Description */}
              {product.short_description && (
                <p className={styles.shortDescription}>
                  {product.short_description}
                </p>
              )}

              {/* Variant Selection */}
              {product.variants.length > 1 && (
                <div className={styles.variantSection}>
                  <h3 className={styles.variantTitle}>Options:</h3>
                  <div className={styles.variants}>
                    {product.variants.map((variant) => (
                      <button
                        key={variant.id}
                        onClick={() => setSelectedVariant(variant)}
                        className={`${styles.variantButton} ${
                          selectedVariant?.id === variant.id ? styles.active : ''
                        }`}
                        disabled={!variant.is_active || variant.inventory_quantity === 0}
                      >
                        {variant.attributes.map(attr => attr.value).join(' / ')}
                        {variant.inventory_quantity === 0 && (
                          <span className={styles.outOfStock}>Out of Stock</span>
                        )}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Quantity and Add to Cart */}
              <div className={styles.purchaseSection}>
                <div className={styles.quantitySection}>
                  <label className={styles.quantityLabel}>Quantity:</label>
                  <div className={styles.quantityControls}>
                    <button
                      onClick={() => handleQuantityChange(quantity - 1)}
                      disabled={quantity <= 1}
                      className={styles.quantityButton}
                    >
                      <FiMinus size={16} />
                    </button>
                    <span className={styles.quantity}>{quantity}</span>
                    <button
                      onClick={() => handleQuantityChange(quantity + 1)}
                      disabled={quantity >= (currentVariant?.inventory_quantity || 0)}
                      className={styles.quantityButton}
                    >
                      <FiPlus size={16} />
                    </button>
                  </div>
                </div>

                <div className={styles.actions}>
                  <Button
                    variant="primary"
                    size="lg"
                    onClick={handleAddToCart}
                    loading={isAddingToCart}
                    disabled={!currentVariant?.is_active || currentVariant?.inventory_quantity === 0}
                    leftIcon={<FiShoppingCart size={20} />}
                    fullWidth
                  >
                    Add to Cart
                  </Button>
                  
                  <Button
                    variant="secondary"
                    size="lg"
                    leftIcon={<FiHeart size={20} />}
                    onClick={handleToggleWishlist}
                    fullWidth
                  >
                    {isInWishlist ? 'Remove from Wishlist' : 'Add to Wishlist'}
                  </Button>
                </div>
              </div>

              {/* Product Features */}
              <div className={styles.features}>
                <div className={styles.feature}>
                  <FiTruck size={20} />
                  <span>Free shipping on orders over $50</span>
                </div>
                <div className={styles.feature}>
                  <FiShield size={20} />
                  <span>Secure payment & data protection</span>
                </div>
                <div className={styles.feature}>
                  <FiRefreshCw size={20} />
                  <span>30-day return policy</span>
                </div>
              </div>
            </div>
          </div>

          {/* Product Description */}
          <div className={styles.descriptionSection}>
            <h2 className={styles.sectionTitle}>Product Description</h2>
            <div 
              className={styles.description}
              dangerouslySetInnerHTML={{ __html: product.description }}
            />
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
