.productsPage {
  min-height: 100vh;
  background: $gray-50;
  padding: $spacing-6 0;
}

.container {
  @include container;
}

.header {
  @include flexbox(space-between, center);
  margin-bottom: $spacing-6;
  gap: $spacing-4;
  
  @include mobile-only {
    flex-direction: column;
    align-items: flex-start;
  }
}

.headerLeft {
  flex: 1;
}

.title {
  font-size: clamp(1.75rem, 4vw, 2.5rem);
  font-weight: 700;
  color: $primary-dark-text-color;
  margin: 0 0 $spacing-2;
}

.resultCount {
  color: $primary-lighter-text-color;
  margin: 0;
}

.headerRight {
  @include flexbox(flex-end, center);
  gap: $spacing-3;
  
  @include mobile-only {
    width: 100%;
    justify-content: space-between;
  }
}

.viewModeToggle {
  @include flexbox(center, center);
  border: 1px solid $gray-300;
  border-radius: $border-radius-md;
  overflow: hidden;
  
  > button {
    border-radius: 0;
    border: none;
    
    &:not(:last-child) {
      border-right: 1px solid $gray-300;
    }
  }
}

.mobileFilterToggle {
  @include laptop-up {
    display: none;
  }
}

.content {
  @include flexbox(flex-start, flex-start);
  gap: $spacing-6;
  align-items: flex-start;
}

.sidebar {
  width: 280px;
  flex-shrink: 0;
  
  @media (max-width: #{$laptop - 1px}) {
    display: none;
    
    &.showMobile {
      display: block;
    }
  }
}

.main {
  flex: 1;
  min-width: 0;
}

.loading {
  @include flexbox(center, center, column);
  gap: $spacing-4;
  padding: $spacing-20 0;
  text-align: center;
  
  p {
    color: $primary-lighter-text-color;
    margin: 0;
  }
}

.error {
  @include flexbox(center, center, column);
  gap: $spacing-4;
  padding: $spacing-20 0;
  text-align: center;
  
  h1 {
    color: $error;
    margin: 0;
  }
  
  p {
    color: $primary-lighter-text-color;
    margin: 0;
  }
}

.productGrid {
  display: grid;
  gap: $spacing-6;
  
  &.grid {
    @include grid-responsive(1, 2, 3, 4);
  }
  
  &.list {
    grid-template-columns: 1fr;
    gap: $spacing-4;
  }
}

.pagination {
  margin-top: $spacing-8;
  padding: $spacing-4;
  text-align: center;
  background: white;
  border-radius: $border-radius-lg;
  border: 1px solid $gray-200;
}

.noResults {
  @include flexbox(center, center, column);
  gap: $spacing-4;
  padding: $spacing-20 0;
  text-align: center;
  color: $gray-500;
  
  h2 {
    color: $primary-dark-text-color;
    margin: 0;
  }
  
  p {
    margin: 0;
  }
}
