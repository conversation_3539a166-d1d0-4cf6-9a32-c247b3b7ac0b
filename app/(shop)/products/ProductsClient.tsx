'use client';

// Client-side products page with filtering and pagination
// Handles product listing, filters, and search functionality

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { MainLayout } from '../../../src/components/layout/MainLayout';
import { Button } from '../../../src/components/ui/Button';
import { Input } from '../../../src/components/ui/Input';
import { ProductCard } from '../../../src/components/features/ProductCard';
import { ProductFilters } from '../../../src/components/features/ProductFilters';
import { LoadingSpinner } from '../../../src/components/ui/LoadingSpinner';
import { useProducts } from '../../../src/hooks/queries/useProducts';
import { useFilterStore } from '../../../src/stores/filter-store';
import { <PERSON><PERSON>ilt<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ist, FiSearch } from 'react-icons/fi';
import styles from './ProductsClient.module.scss';

export function ProductsClient() {
  const searchParams = useSearchParams();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  
  const { filters, setFilter } = useFilterStore();
  
  // Sync URL params with filter store
  useEffect(() => {
    const urlFilters: any = {};
    
    // Extract filters from URL
    const category = searchParams.get('category');
    const search = searchParams.get('search') || searchParams.get('q');
    const sort = searchParams.get('sort');
    const page = searchParams.get('page');
    const minPrice = searchParams.get('min_price');
    const maxPrice = searchParams.get('max_price');
    
    if (category) urlFilters.category = category;
    if (search) urlFilters.search = search;
    if (sort) urlFilters.sort = sort;
    if (page) urlFilters.page = parseInt(page);
    if (minPrice) urlFilters.min_price = parseFloat(minPrice);
    if (maxPrice) urlFilters.max_price = parseFloat(maxPrice);
    
    // Update filter store if URL has filters
    if (Object.keys(urlFilters).length > 0) {
      Object.entries(urlFilters).forEach(([key, value]) => {
        setFilter(key as any, value);
      });
    }
  }, [searchParams, setFilter]);

  const { data, isLoading, error } = useProducts(filters);

  const handleViewModeChange = (mode: 'grid' | 'list') => {
    setViewMode(mode);
  };

  if (error) {
    return (
      <MainLayout>
        <div className={styles.error}>
          <h1>Something went wrong</h1>
          <p>Failed to load products. Please try again later.</p>
          <Button onClick={() => window.location.reload()}>
            Retry
          </Button>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className={styles.productsPage}>
        <div className={styles.container}>
          {/* Header */}
          <div className={styles.header}>
            <div className={styles.headerLeft}>
              <h1 className={styles.title}>Products</h1>
              {data && (
                <p className={styles.resultCount}>
                  {data.count} products found
                </p>
              )}
            </div>
            
            <div className={styles.headerRight}>
              {/* View Mode Toggle */}
              <div className={styles.viewModeToggle}>
                <Button
                  variant={viewMode === 'grid' ? 'primary' : 'ghost'}
                  size="sm"
                  onClick={() => handleViewModeChange('grid')}
                >
                  <FiGrid size={16} />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'primary' : 'ghost'}
                  size="sm"
                  onClick={() => handleViewModeChange('list')}
                >
                  <FiList size={16} />
                </Button>
              </div>
              
              {/* Mobile Filter Toggle */}
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className={styles.mobileFilterToggle}
              >
                <FiFilter size={16} />
                Filters
              </Button>
            </div>
          </div>

          <div className={styles.content}>
            {/* Sidebar Filters */}
            <aside className={`${styles.sidebar} ${showFilters ? styles.showMobile : ''}`}>
              <ProductFilters onClose={() => setShowFilters(false)} />
            </aside>

            {/* Main Content */}
            <main className={styles.main}>
              {isLoading ? (
                <div className={styles.loading}>
                  <LoadingSpinner size="lg" />
                  <p>Loading products...</p>
                </div>
              ) : data?.results && data.results.length > 0 ? (
                <>
                  <div className={`${styles.productGrid} ${styles[viewMode]}`}>
                    {data.results.map((product) => (
                      <ProductCard
                        key={product.id}
                        product={product}
                        viewMode={viewMode}
                      />
                    ))}
                  </div>
                  
                  {/* Pagination would go here */}
                  {data.total_pages > 1 && (
                    <div className={styles.pagination}>
                      <p>Pagination component would go here</p>
                    </div>
                  )}
                </>
              ) : (
                <div className={styles.noResults}>
                  <FiSearch size={48} />
                  <h2>No products found</h2>
                  <p>Try adjusting your filters or search terms.</p>
                  <Button variant="primary">
                    Clear Filters
                  </Button>
                </div>
              )}
            </main>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
