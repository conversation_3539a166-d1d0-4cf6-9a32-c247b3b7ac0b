import { MainLayout } from '../src/components/layout/MainLayout';
import { Button } from '../src/components/ui/Button';
import { FiShoppingBag, FiTrendingUp, FiShield, FiTruck } from 'react-icons/fi';
import styles from './page.module.scss';

export default function Home() {
  return (
    <MainLayout>
      {/* Hero Section */}
      <section className={styles.hero}>
        <div className={styles.heroContainer}>
          <div className={styles.heroContent}>
            <h1 className={styles.heroTitle}>
              Discover Amazing Products at
              <span className={styles.highlight}> Picky Store</span>
            </h1>
            <p className={styles.heroDescription}>
              Your trusted e-commerce destination for quality products,
              exceptional service, and unbeatable prices. Shop with confidence.
            </p>
            <div className={styles.heroActions}>
              <Button variant="primary" size="lg">
                <FiShoppingBag size={20} />
                Shop Now
              </Button>
              <Button variant="secondary" size="lg">
                Explore Categories
              </Button>
            </div>
          </div>
          <div className={styles.heroImage}>
            <div className={styles.placeholder}>
              🛍️
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className={styles.features}>
        <div className={styles.container}>
          <h2 className={styles.sectionTitle}>Why Choose Picky Store?</h2>
          <div className={styles.featureGrid}>
            <div className={styles.feature}>
              <div className={styles.featureIcon}>
                <FiTrendingUp size={32} />
              </div>
              <h3 className={styles.featureTitle}>Quality Products</h3>
              <p className={styles.featureDescription}>
                Carefully curated selection of high-quality products from trusted brands.
              </p>
            </div>

            <div className={styles.feature}>
              <div className={styles.featureIcon}>
                <FiShield size={32} />
              </div>
              <h3 className={styles.featureTitle}>Secure Shopping</h3>
              <p className={styles.featureDescription}>
                Your data and payments are protected with industry-leading security.
              </p>
            </div>

            <div className={styles.feature}>
              <div className={styles.featureIcon}>
                <FiTruck size={32} />
              </div>
              <h3 className={styles.featureTitle}>Fast Delivery</h3>
              <p className={styles.featureDescription}>
                Quick and reliable shipping to get your orders to you faster.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className={styles.cta}>
        <div className={styles.container}>
          <div className={styles.ctaContent}>
            <h2 className={styles.ctaTitle}>Ready to Start Shopping?</h2>
            <p className={styles.ctaDescription}>
              Join thousands of satisfied customers and discover your next favorite product.
            </p>
            <Button variant="primary" size="lg">
              Browse Products
            </Button>
          </div>
        </div>
      </section>
    </MainLayout>
  );
}
