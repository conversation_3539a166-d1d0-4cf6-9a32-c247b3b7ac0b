/* @import '../src/styles/variables.scss';
@import '../src/styles/mixins.scss'; */

/* CSS Reset */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

body {
  font-family: $primary-font-family;
  color: $primary-dark-text-color;
  background-color: $gray-50;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  max-width: 100vw;
  overflow-x: hidden;
}

/* Remove default button styles */
button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  font: inherit;
  cursor: pointer;
}

/* Remove default link styles */
a {
  color: inherit;
  text-decoration: none;
}

/* Remove default list styles */
ul,
ol {
  list-style: none;
}

/* Remove default input styles */
input,
textarea,
select {
  font: inherit;
  color: inherit;
}

/* Image defaults */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Focus styles for accessibility */
:focus-visible {
  outline: 2px solid $primary-blue;
  outline-offset: 2px;
}

/* Utility classes */
.container {
  @include container;
}

.sr-only {
  @include visually-hidden;
}

.text-truncate {
  @include text-truncate;
}

/* Loading animations */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {

  0%,
  100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }

  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}