---
type: "agent_requested"
description: "React, TypeScript coding best practices. "
---
- Prioritize available, popular, reputable, maintained npm packages for completing tasks over creating custom utility functions for every task. Validate package health (≥1k weekly downloads, recent commits) before "using" it in logic.
- Always use TypeScript with strict type safety — no any, avoid unsafe casts.
- Structure components by responsibility; favor functional components with hooks.
- Write declarative code — avoid imperative manipulation of DOM/state.
- Use this general folder structure: src/ folders: components/, features/, hooks/, utils/, types/, styles/, pages/.
- Isolate side effects and external service logic into dedicated layers.
- Document third-party packages and their intended purpose.
- Prioritize accessibility (semantic HTML, keyboard nav, a11y linting).
- Handle async errors with try/catch and show user-friendly messages.
- Test core logic (vitest, jest) and UI (react-testing-library).
- Use environment variables for all configs and secrets — never hardcode.
