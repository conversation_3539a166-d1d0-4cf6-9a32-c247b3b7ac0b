// SEO and meta tags utilities
// Provides utilities for better SEO and social media sharing

import { Metadata } from 'next'

interface SEOConfig {
  title?: string
  description?: string
  keywords?: string[]
  image?: string
  url?: string
  type?: 'website' | 'article' | 'product'
  siteName?: string
  locale?: string
  author?: string
  publishedTime?: string
  modifiedTime?: string
  section?: string
  tags?: string[]
  price?: {
    amount: number
    currency: string
  }
  availability?: 'in stock' | 'out of stock' | 'preorder'
  brand?: string
  category?: string
  noIndex?: boolean
  noFollow?: boolean
}

const defaultConfig = {
  siteName: 'Picky Store',
  locale: 'en_US',
  type: 'website' as const,
  image: '/images/og-default.jpg',
  author: 'Picky Store Team',
}

export function generateMetadata(config: SEOConfig): Metadata {
  const {
    title,
    description,
    keywords = [],
    image,
    url,
    type = 'website',
    siteName = defaultConfig.siteName,
    locale = defaultConfig.locale,
    author = defaultConfig.author,
    publishedTime,
    modifiedTime,
    section,
    tags = [],
    price,
    availability,
    brand,
    category,
    noIndex = false,
    noFollow = false,
  } = config

  const fullTitle = title ? `${title} | ${siteName}` : siteName
  const imageUrl = image || defaultConfig.image
  const fullUrl = url ? `https://pickystore.com${url}` : 'https://pickystore.com'

  const metadata: Metadata = {
    title: fullTitle,
    description,
    keywords: keywords.join(', '),
    authors: [{ name: author }],
    creator: author,
    publisher: siteName,
    robots: {
      index: !noIndex,
      follow: !noFollow,
      googleBot: {
        index: !noIndex,
        follow: !noFollow,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    openGraph: {
      title: fullTitle,
      description,
      url: fullUrl,
      siteName,
      images: [
        {
          url: imageUrl,
          width: 1200,
          height: 630,
          alt: title || siteName,
        },
      ],
      locale,
      type,
    },
    twitter: {
      card: 'summary_large_image',
      title: fullTitle,
      description,
      images: [imageUrl],
      creator: '@pickystore',
      site: '@pickystore',
    },
    alternates: {
      canonical: fullUrl,
    },
    other: {},
  }

  // Add article-specific metadata
  if (type === 'article' && publishedTime) {
    metadata.openGraph = {
      ...metadata.openGraph,
      type: 'article',
      publishedTime,
      modifiedTime,
      section,
      tags,
      authors: [author],
    }
  }

  // Add product-specific metadata
  if (type === 'product') {
    metadata.openGraph = {
      ...metadata.openGraph,
      type: 'product',
    }

    // Add structured data for products
    if (price) {
      metadata.other = {
        ...metadata.other,
        'product:price:amount': price.amount.toString(),
        'product:price:currency': price.currency,
      }
    }

    if (availability) {
      metadata.other = {
        ...metadata.other,
        'product:availability': availability,
      }
    }

    if (brand) {
      metadata.other = {
        ...metadata.other,
        'product:brand': brand,
      }
    }

    if (category) {
      metadata.other = {
        ...metadata.other,
        'product:category': category,
      }
    }
  }

  return metadata
}

// Generate structured data for products
export function generateProductStructuredData(product: any) {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: product.name,
    description: product.description || product.short_description,
    image: product.images?.map((img: any) => img.image) || [],
    brand: {
      '@type': 'Brand',
      name: product.brand?.name || 'Picky Store',
    },
    category: product.category?.name,
    sku: product.sku,
    offers: {
      '@type': 'Offer',
      price: product.min_price,
      priceCurrency: 'USD',
      availability: product.is_available 
        ? 'https://schema.org/InStock' 
        : 'https://schema.org/OutOfStock',
      seller: {
        '@type': 'Organization',
        name: 'Picky Store',
      },
    },
    aggregateRating: product.average_rating ? {
      '@type': 'AggregateRating',
      ratingValue: product.average_rating,
      reviewCount: product.review_count || 0,
      bestRating: 5,
      worstRating: 1,
    } : undefined,
  }

  return JSON.stringify(structuredData)
}

// Generate structured data for organization
export function generateOrganizationStructuredData() {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'Picky Store',
    url: 'https://pickystore.com',
    logo: 'https://pickystore.com/images/logo.png',
    description: 'Your trusted online store for quality products',
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '******-123-4567',
      contactType: 'Customer Service',
      availableLanguage: 'English',
    },
    sameAs: [
      'https://facebook.com/pickystore',
      'https://twitter.com/pickystore',
      'https://instagram.com/pickystore',
    ],
    address: {
      '@type': 'PostalAddress',
      streetAddress: '123 Commerce Street',
      addressLocality: 'New York',
      addressRegion: 'NY',
      postalCode: '10001',
      addressCountry: 'US',
    },
  }

  return JSON.stringify(structuredData)
}

// Generate structured data for breadcrumbs
export function generateBreadcrumbStructuredData(breadcrumbs: Array<{ name: string; url: string }>) {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((crumb, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: crumb.name,
      item: `https://pickystore.com${crumb.url}`,
    })),
  }

  return JSON.stringify(structuredData)
}

// Generate structured data for search results
export function generateSearchResultsStructuredData(query: string, results: any[]) {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'SearchResultsPage',
    mainEntity: {
      '@type': 'ItemList',
      numberOfItems: results.length,
      itemListElement: results.map((result, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        url: `https://pickystore.com/products/${result.slug}`,
        name: result.name,
        description: result.short_description,
        image: result.images?.[0]?.image,
      })),
    },
    potentialAction: {
      '@type': 'SearchAction',
      target: `https://pickystore.com/search?q=${encodeURIComponent(query)}`,
      'query-input': 'required name=q',
    },
  }

  return JSON.stringify(structuredData)
}

// Generate robots.txt content
export function generateRobotsTxt() {
  return `User-agent: *
Allow: /

# Sitemaps
Sitemap: https://pickystore.com/sitemap.xml
Sitemap: https://pickystore.com/products-sitemap.xml
Sitemap: https://pickystore.com/categories-sitemap.xml

# Disallow admin and private areas
Disallow: /admin/
Disallow: /api/
Disallow: /_next/
Disallow: /account/
Disallow: /checkout/

# Allow important pages
Allow: /products/
Allow: /categories/
Allow: /brands/
Allow: /search

# Crawl delay
Crawl-delay: 1`
}

// Generate sitemap URLs
export function generateSitemapUrls() {
  const baseUrl = 'https://pickystore.com'
  const staticPages = [
    { url: '/', priority: 1.0, changefreq: 'daily' },
    { url: '/products', priority: 0.9, changefreq: 'daily' },
    { url: '/categories', priority: 0.8, changefreq: 'weekly' },
    { url: '/brands', priority: 0.8, changefreq: 'weekly' },
    { url: '/about', priority: 0.6, changefreq: 'monthly' },
    { url: '/contact', priority: 0.6, changefreq: 'monthly' },
    { url: '/privacy', priority: 0.3, changefreq: 'yearly' },
    { url: '/terms', priority: 0.3, changefreq: 'yearly' },
  ]

  return staticPages.map(page => ({
    url: `${baseUrl}${page.url}`,
    lastModified: new Date().toISOString(),
    changeFrequency: page.changefreq as 'daily' | 'weekly' | 'monthly' | 'yearly',
    priority: page.priority,
  }))
}

// SEO-friendly URL slug generator
export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
}

// Meta description generator
export function generateMetaDescription(text: string, maxLength = 160): string {
  if (text.length <= maxLength) return text
  
  const truncated = text.substring(0, maxLength - 3)
  const lastSpace = truncated.lastIndexOf(' ')
  
  return lastSpace > 0 
    ? truncated.substring(0, lastSpace) + '...'
    : truncated + '...'
}

// Keywords extractor
export function extractKeywords(text: string, maxKeywords = 10): string[] {
  const commonWords = new Set([
    'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
    'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
    'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'
  ])

  const words = text
    .toLowerCase()
    .replace(/[^\w\s]/g, '')
    .split(/\s+/)
    .filter(word => word.length > 2 && !commonWords.has(word))

  const wordCount = words.reduce((acc, word) => {
    acc[word] = (acc[word] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  return Object.entries(wordCount)
    .sort(([, a], [, b]) => b - a)
    .slice(0, maxKeywords)
    .map(([word]) => word)
}
