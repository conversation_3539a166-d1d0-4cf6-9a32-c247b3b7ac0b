// Testing utilities and helpers
// Provides utilities for testing React components and hooks

import React from 'react'
import { render, RenderOptions, RenderResult } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import userEvent from '@testing-library/user-event'

// Mock data generators
export const mockProduct = (overrides = {}) => ({
  id: '1',
  name: 'Test Product',
  slug: 'test-product',
  description: 'A test product description',
  short_description: 'Test product',
  min_price: 29.99,
  max_price: 39.99,
  average_rating: 4.5,
  review_count: 10,
  is_available: true,
  images: [
    {
      id: '1',
      image: '/test-image.jpg',
      alt_text: 'Test image',
      is_primary: true,
    },
  ],
  variants: [
    {
      id: '1',
      price: 29.99,
      compare_at_price: 39.99,
      is_active: true,
      inventory_quantity: 10,
      attributes: [
        { name: 'Color', value: 'Blue' },
        { name: '<PERSON><PERSON>', value: 'Medium' },
      ],
    },
  ],
  category: {
    id: '1',
    name: 'Test Category',
    slug: 'test-category',
  },
  brand: {
    id: '1',
    name: 'Test Brand',
    slug: 'test-brand',
  },
  ...overrides,
})

export const mockUser = (overrides = {}) => ({
  id: '1',
  email: '<EMAIL>',
  first_name: 'John',
  last_name: 'Doe',
  is_email_verified: true,
  date_joined: '2024-01-01T00:00:00Z',
  addresses: [],
  order_count: 0,
  wishlist_count: 0,
  ...overrides,
})

export const mockCartItem = (overrides = {}) => ({
  id: '1',
  product: mockProduct(),
  variant: mockProduct().variants[0],
  quantity: 1,
  added_at: '2024-01-01T00:00:00Z',
  ...overrides,
})

export const mockOrder = (overrides = {}) => ({
  id: '1',
  order_number: 'ORD-2024-001',
  status: 'delivered',
  total_amount: 129.99,
  subtotal: 119.99,
  tax: 9.60,
  shipping: 0,
  created_at: '2024-01-15T10:30:00Z',
  items: [mockCartItem()],
  shipping_address: {
    name: 'John Doe',
    address_line_1: '123 Main St',
    city: 'New York',
    state: 'NY',
    zip_code: '10001',
    country: 'United States',
  },
  ...overrides,
})

export const mockCategory = (overrides = {}) => ({
  id: '1',
  name: 'Test Category',
  slug: 'test-category',
  description: 'A test category',
  image: '/test-category.jpg',
  parent: null,
  children: [],
  ...overrides,
})

export const mockBrand = (overrides = {}) => ({
  id: '1',
  name: 'Test Brand',
  slug: 'test-brand',
  description: 'A test brand',
  logo: '/test-brand-logo.jpg',
  website: 'https://testbrand.com',
  ...overrides,
})

// Test providers wrapper
interface TestProvidersProps {
  children: React.ReactNode
  queryClient?: QueryClient
}

export function TestProviders({ children, queryClient }: TestProvidersProps) {
  const defaultQueryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        staleTime: Infinity,
      },
    },
  })

  return (
    <QueryClientProvider client={queryClient || defaultQueryClient}>
      {children}
    </QueryClientProvider>
  )
}

// Custom render function with providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  queryClient?: QueryClient
}

export function renderWithProviders(
  ui: React.ReactElement,
  options: CustomRenderOptions = {}
): RenderResult & { user: ReturnType<typeof userEvent.setup> } {
  const { queryClient, ...renderOptions } = options

  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <TestProviders queryClient={queryClient}>{children}</TestProviders>
  )

  const result = render(ui, { wrapper: Wrapper, ...renderOptions })
  const user = userEvent.setup()

  return {
    ...result,
    user,
  }
}

// Mock API responses
export const mockApiResponse = <T,>(data: T, delay = 0) => {
  return new Promise<T>((resolve) => {
    setTimeout(() => resolve(data), delay)
  })
}

export const mockApiError = (message = 'API Error', status = 500, delay = 0) => {
  return new Promise((_, reject) => {
    setTimeout(() => {
      const error = new Error(message)
      ;(error as any).status = status
      reject(error)
    }, delay)
  })
}

// Mock intersection observer
export const mockIntersectionObserver = () => {
  const mockIntersectionObserver = jest.fn()
  mockIntersectionObserver.mockReturnValue({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null,
  })
  window.IntersectionObserver = mockIntersectionObserver
}

// Mock resize observer
export const mockResizeObserver = () => {
  const mockResizeObserver = jest.fn()
  mockResizeObserver.mockReturnValue({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null,
  })
  window.ResizeObserver = mockResizeObserver
}

// Mock local storage
export const mockLocalStorage = () => {
  const localStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  }
  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock,
  })
  return localStorageMock
}

// Mock session storage
export const mockSessionStorage = () => {
  const sessionStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  }
  Object.defineProperty(window, 'sessionStorage', {
    value: sessionStorageMock,
  })
  return sessionStorageMock
}

// Mock window.matchMedia
export const mockMatchMedia = (matches = false) => {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation((query) => ({
      matches,
      media: query,
      onchange: null,
      addListener: jest.fn(),
      removeListener: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    })),
  })
}

// Mock fetch
export const mockFetch = (response: any, ok = true, status = 200) => {
  global.fetch = jest.fn().mockResolvedValue({
    ok,
    status,
    json: jest.fn().mockResolvedValue(response),
    text: jest.fn().mockResolvedValue(JSON.stringify(response)),
  })
}

// Test utilities for async operations
export const waitForLoadingToFinish = () => {
  return new Promise((resolve) => {
    setTimeout(resolve, 0)
  })
}

// Custom matchers for testing
export const customMatchers = {
  toBeInTheDocument: (received: any) => {
    const pass = received !== null && received !== undefined
    return {
      message: () =>
        pass
          ? `Expected element not to be in the document`
          : `Expected element to be in the document`,
      pass,
    }
  },
  toHaveClass: (received: any, className: string) => {
    const pass = received.classList.contains(className)
    return {
      message: () =>
        pass
          ? `Expected element not to have class "${className}"`
          : `Expected element to have class "${className}"`,
      pass,
    }
  },
}

// Setup function for tests
export const setupTests = () => {
  mockIntersectionObserver()
  mockResizeObserver()
  mockLocalStorage()
  mockSessionStorage()
  mockMatchMedia()
  
  // Mock console methods to reduce noise in tests
  jest.spyOn(console, 'warn').mockImplementation(() => {})
  jest.spyOn(console, 'error').mockImplementation(() => {})
}

// Cleanup function for tests
export const cleanupTests = () => {
  jest.restoreAllMocks()
  jest.clearAllMocks()
}

// Test data factories
export const createTestProduct = (count = 1) => {
  return Array.from({ length: count }, (_, index) =>
    mockProduct({
      id: `${index + 1}`,
      name: `Test Product ${index + 1}`,
      slug: `test-product-${index + 1}`,
    })
  )
}

export const createTestUsers = (count = 1) => {
  return Array.from({ length: count }, (_, index) =>
    mockUser({
      id: `${index + 1}`,
      email: `user${index + 1}@example.com`,
      first_name: `User${index + 1}`,
    })
  )
}

export const createTestOrders = (count = 1) => {
  return Array.from({ length: count }, (_, index) =>
    mockOrder({
      id: `${index + 1}`,
      order_number: `ORD-2024-${String(index + 1).padStart(3, '0')}`,
    })
  )
}

// Performance testing utilities
export const measureRenderTime = (component: React.ReactElement) => {
  const start = performance.now()
  renderWithProviders(component)
  const end = performance.now()
  return end - start
}

// Accessibility testing helpers
export const checkAccessibility = async (container: HTMLElement) => {
  // This would integrate with axe-core or similar
  // For now, just check basic accessibility features
  const issues = []
  
  // Check for alt text on images
  const images = container.querySelectorAll('img')
  images.forEach((img, index) => {
    if (!img.alt) {
      issues.push(`Image ${index + 1} missing alt text`)
    }
  })
  
  // Check for form labels
  const inputs = container.querySelectorAll('input, textarea, select')
  inputs.forEach((input, index) => {
    const id = input.getAttribute('id')
    const label = container.querySelector(`label[for="${id}"]`)
    if (!label && !input.getAttribute('aria-label')) {
      issues.push(`Input ${index + 1} missing label`)
    }
  })
  
  return issues
}
