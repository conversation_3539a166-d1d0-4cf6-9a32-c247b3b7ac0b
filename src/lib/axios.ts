// Axios configuration for e-commerce API client
// Cookie-based authentication with interceptors

import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import type { ApiResponse, ApiError } from '../types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:8000';

// Create the main axios instance with cookie support
export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  withCredentials: true, // Important: This sends HTTP-only cookies automatically
  headers: {
    'Content-Type': 'application/json',
  },
});

// Response interceptor for error handling
// Token refresh is handled server-side automatically
apiClient.interceptors.response.use(
  (response: AxiosResponse) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };

    // Handle 401 errors - try token refresh once
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      try {
        // Attempt server-side token refresh
        const refreshResponse = await axios.post(
          `${API_BASE_URL}/api/auth/token/refresh/`,
          {},
          { withCredentials: true }
        );

        if (refreshResponse.data.success) {
          // Server has updated HTTP-only cookies, retry original request
          return apiClient(originalRequest);
        } else {
          throw new Error('Token refresh failed');
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        if (typeof window !== 'undefined') {
          window.location.href = '/auth/login';
        }
        return Promise.reject(refreshError);
      }
    }

    // Handle other errors
    const apiError: ApiError = {
      message: error.message || 'An error occurred',
      status: error.response?.status || 500,
      errors: error.response?.data?.errors,
    };

    return Promise.reject(apiError);
  }
);

/**
 * Generic API client class for CRUD operations
 * Based on admin-arena APIClient pattern
 */
export class APIClient<TResponse, TRequest = TResponse> {
  endpoint: string;

  constructor(endpoint: string) {
    this.endpoint = endpoint;
  }

  /**
   * GET single resource
   */
  get = async (config?: AxiosRequestConfig): Promise<TResponse> => {
    try {
      const response = await apiClient.get<TResponse>(this.endpoint, config);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  };

  /**
   * GET paginated list of resources
   */
  getAll = async (config?: AxiosRequestConfig): Promise<TResponse> => {
    try {
      const response = await apiClient.get<TResponse>(this.endpoint, config);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  };

  /**
   * POST create new resource
   */
  post = async (data: TRequest, config?: AxiosRequestConfig): Promise<TResponse> => {
    try {
      const response = await apiClient.post<TResponse>(this.endpoint, data, config);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  };

  /**
   * PUT update resource (full update)
   */
  put = async (data: TRequest, config?: AxiosRequestConfig): Promise<TResponse> => {
    try {
      const response = await apiClient.put<TResponse>(this.endpoint, data, config);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  };

  /**
   * PATCH update resource (partial update)
   */
  patch = async (data: Partial<TRequest>, config?: AxiosRequestConfig): Promise<TResponse> => {
    try {
      const response = await apiClient.patch<TResponse>(this.endpoint, data, config);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  };

  /**
   * DELETE resource
   */
  delete = async (config?: AxiosRequestConfig): Promise<void> => {
    try {
      await apiClient.delete(this.endpoint, config);
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  };

  /**
   * Handle and log errors
   */
  private handleError = (error: unknown): void => {
    if (axios.isAxiosError(error)) {
      // Don't log cancelled requests as errors
      if (error.code === 'ERR_CANCELED' || error.message === 'canceled') {
        throw error;
      }

      // Don't log expected authentication errors to reduce console noise
      const isAuthError = error.response?.status === 401 || error.response?.status === 403;

      if (!isAuthError && process.env.NODE_ENV === 'development') {
        console.error('API Error:', {
          message: error.message,
          status: error.response?.status,
          data: error.response?.data,
          url: error.config?.url,
        });
      }
    } else {
      console.error('Unexpected error:', error);
    }
  };
}

// Utility functions for common API operations
export const apiUtils = {
  /**
   * Build query string from object
   */
  buildQueryString: (params: Record<string, any>): string => {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value)) {
          value.forEach(item => searchParams.append(key, item.toString()));
        } else {
          searchParams.append(key, value.toString());
        }
      }
    });
    
    return searchParams.toString();
  },

  /**
   * Create API client instance
   */
  createClient: <TResponse, TRequest = TResponse>(endpoint: string) => {
    return new APIClient<TResponse, TRequest>(endpoint);
  },

  /**
   * Handle file upload
   */
  uploadFile: async (endpoint: string, file: File, additionalData?: Record<string, any>) => {
    const formData = new FormData();
    formData.append('file', file);
    
    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, value.toString());
      });
    }

    try {
      const response = await apiClient.post(endpoint, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error('File upload error:', error);
      throw error;
    }
  },

  /**
   * Download file
   */
  downloadFile: async (endpoint: string, filename?: string) => {
    try {
      const response = await apiClient.get(endpoint, {
        responseType: 'blob',
      });

      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', filename || 'download');
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('File download error:', error);
      throw error;
    }
  },
};

export default apiClient;
