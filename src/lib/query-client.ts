import { QueryClient } from '@tanstack/react-query'

// Create a client with default options
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Time before data is considered stale (5 minutes)
      staleTime: 5 * 60 * 1000,
      // Time before inactive queries are garbage collected (10 minutes)
      gcTime: 10 * 60 * 1000,
      // Retry failed requests up to 3 times
      retry: 3,
      // Don't retry on 4xx errors
      retryOnMount: false,
      // Refetch on window focus in development
      refetchOnWindowFocus: process.env.NODE_ENV === 'development',
    },
    mutations: {
      // Retry failed mutations up to 2 times
      retry: 2,
    },
  },
})
