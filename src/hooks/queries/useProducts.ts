// Product-related TanStack Query hooks
// Handles product fetching, search, and related operations

import { useQuery, useInfiniteQuery } from '@tanstack/react-query';
import { ProductService } from '../../services/api-client';
import { queryKeys } from '../../services/query-keys';
import type { ProductFilters, Product, PaginatedResponse } from '../../types';

// Get paginated products with filters
export function useProducts(filters?: ProductFilters) {
  return useQuery({
    queryKey: queryKeys.products.list(filters),
    queryFn: () => ProductService.getProducts(filters),
    staleTime: 10 * 60 * 1000, // 10 minutes
    keepPreviousData: true, // Keep previous data while loading new filters
  });
}

// Get infinite products for pagination
export function useInfiniteProducts(filters?: ProductFilters) {
  return useInfiniteQuery({
    queryKey: queryKeys.products.list(filters),
    queryFn: ({ pageParam = 1 }) => 
      ProductService.getProducts({ ...filters, page: pageParam }),
    getNextPageParam: (lastPage: PaginatedResponse<Product>) => {
      if (lastPage.next) {
        const url = new URL(lastPage.next);
        const page = url.searchParams.get('page');
        return page ? parseInt(page) : undefined;
      }
      return undefined;
    },
    staleTime: 10 * 60 * 1000,
    keepPreviousData: true,
  });
}

// Get single product by slug
export function useProduct(slug: string, enabled = true) {
  return useQuery({
    queryKey: queryKeys.products.detail(slug),
    queryFn: () => ProductService.getProduct(slug),
    enabled: enabled && !!slug,
    staleTime: 15 * 60 * 1000, // 15 minutes
  });
}

// Get featured products
export function useFeaturedProducts() {
  return useQuery({
    queryKey: queryKeys.products.featured(),
    queryFn: ProductService.getFeaturedProducts,
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
}

// Get related products
export function useRelatedProducts(productId: number, enabled = true) {
  return useQuery({
    queryKey: queryKeys.products.related(productId),
    queryFn: () => ProductService.getRelatedProducts(productId),
    enabled: enabled && !!productId,
    staleTime: 20 * 60 * 1000, // 20 minutes
  });
}

// Search products
export function useSearchProducts(query: string, filters?: ProductFilters, enabled = true) {
  return useQuery({
    queryKey: queryKeys.products.search(query),
    queryFn: () => ProductService.searchProducts(query, filters),
    enabled: enabled && !!query.trim(),
    staleTime: 5 * 60 * 1000, // 5 minutes for search results
    keepPreviousData: true,
  });
}

// Search products with infinite scroll
export function useInfiniteSearchProducts(query: string, filters?: ProductFilters, enabled = true) {
  return useInfiniteQuery({
    queryKey: queryKeys.search.results(query, filters),
    queryFn: ({ pageParam = 1 }) =>
      ProductService.searchProducts(query, { ...filters, page: pageParam }),
    getNextPageParam: (lastPage: PaginatedResponse<Product>) => {
      if (lastPage.next) {
        const url = new URL(lastPage.next);
        const page = url.searchParams.get('page');
        return page ? parseInt(page) : undefined;
      }
      return undefined;
    },
    enabled: enabled && !!query.trim(),
    staleTime: 5 * 60 * 1000,
    keepPreviousData: true,
  });
}

// Search products hook for search page
export function useSearchProducts(params: ProductFilters & { search?: string }) {
  return useQuery({
    queryKey: queryKeys.search.results(params.search || '', params),
    queryFn: () => ProductService.searchProducts(params.search || '', params),
    enabled: !!params.search?.trim(),
    staleTime: 5 * 60 * 1000,
    keepPreviousData: true,
  });
}

// Search suggestions hook
export function useSearchSuggestions(query: string, enabled = true) {
  return useQuery({
    queryKey: queryKeys.search.suggestions(query),
    queryFn: () => ProductService.getSearchSuggestions(query),
    enabled: enabled && !!query.trim() && query.length >= 2,
    staleTime: 10 * 60 * 1000,
    keepPreviousData: true,
  });
}
