// Category-related TanStack Query hooks
// Handles category fetching and navigation

import { useQuery } from '@tanstack/react-query';
import { CategoryService } from '../../services/api-client';
import { queryKeys } from '../../services/query-keys';

// Get all categories
export function useCategories() {
  return useQuery({
    queryKey: queryKeys.categories.list(),
    queryFn: CategoryService.getCategories,
    staleTime: 30 * 60 * 1000, // 30 minutes - categories don't change often
  });
}

// Get single category by slug
export function useCategory(slug: string, enabled = true) {
  return useQuery({
    queryKey: queryKeys.categories.detail(slug),
    queryFn: () => CategoryService.getCategory(slug),
    enabled: enabled && !!slug,
    staleTime: 30 * 60 * 1000,
  });
}

// Get category tree (hierarchical structure)
export function useCategoryTree() {
  return useQuery({
    queryKey: queryKeys.categories.tree(),
    queryFn: CategoryService.getCategoryTree,
    staleTime: 30 * 60 * 1000,
  });
}
