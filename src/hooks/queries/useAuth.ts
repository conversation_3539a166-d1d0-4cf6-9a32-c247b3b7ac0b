// Authentication-related TanStack Query hooks
// Handles user authentication state and mutations

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AuthService } from '../../services/api-client';
import { queryKeys } from '../../services/query-keys';
import { useAuthStore } from '../../stores/auth-store';
import { useUIActions } from '../../stores/ui-store';
import type { LoginCredentials, RegisterData, User } from '../../types';

// Get current user query
export function useCurrentUser() {
  const { setUser, setAuthenticated, setLoading } = useAuthStore();

  return useQuery({
    queryKey: queryKeys.auth.user(),
    queryFn: AuthService.getCurrentUser,
    retry: false, // Don't retry auth failures
    staleTime: 5 * 60 * 1000, // 5 minutes
    onSuccess: (user: User) => {
      setUser(user);
      setAuthenticated(true);
      setLoading(false);
    },
    onError: () => {
      setUser(null);
      setAuthenticated(false);
      setLoading(false);
    },
  });
}

// Login mutation
export function useLogin() {
  const queryClient = useQueryClient();
  const { setUser, setAuthenticated } = useAuthStore();
  const { addNotification } = useUIActions();

  return useMutation({
    mutationFn: (credentials: LoginCredentials) => AuthService.login(credentials),
    onSuccess: (response) => {
      setUser(response.user);
      setAuthenticated(true);
      
      // Update user query cache
      queryClient.setQueryData(queryKeys.auth.user(), response.user);
      
      addNotification({
        type: 'success',
        title: 'Welcome back!',
        message: 'You have been successfully logged in.',
      });
    },
    onError: (error: any) => {
      addNotification({
        type: 'error',
        title: 'Login failed',
        message: error.message || 'Invalid credentials. Please try again.',
      });
    },
  });
}

// Register mutation
export function useRegister() {
  const queryClient = useQueryClient();
  const { setUser, setAuthenticated } = useAuthStore();
  const { addNotification } = useUIActions();

  return useMutation({
    mutationFn: (data: RegisterData) => AuthService.register(data),
    onSuccess: (response) => {
      setUser(response.user);
      setAuthenticated(true);
      
      // Update user query cache
      queryClient.setQueryData(queryKeys.auth.user(), response.user);
      
      addNotification({
        type: 'success',
        title: 'Welcome to Picky Store!',
        message: 'Your account has been created successfully.',
      });
    },
    onError: (error: any) => {
      addNotification({
        type: 'error',
        title: 'Registration failed',
        message: error.message || 'Failed to create account. Please try again.',
      });
    },
  });
}

// Logout mutation
export function useLogout() {
  const queryClient = useQueryClient();
  const { logout } = useAuthStore();
  const { addNotification } = useUIActions();

  return useMutation({
    mutationFn: AuthService.logout,
    onSuccess: () => {
      logout();
      
      // Clear all cached data
      queryClient.clear();
      
      addNotification({
        type: 'success',
        title: 'Logged out successfully',
      });
    },
    onError: (error: any) => {
      // Even if logout fails on server, clear local state
      logout();
      queryClient.clear();
      
      addNotification({
        type: 'warning',
        title: 'Logged out',
        message: 'You have been logged out locally.',
      });
    },
  });
}

// Update profile mutation
export function useUpdateProfile() {
  const queryClient = useQueryClient();
  const { setUser } = useAuthStore();
  const { addNotification } = useUIActions();

  return useMutation({
    mutationFn: (data: Partial<User>) => AuthService.updateProfile(data),
    onSuccess: (updatedUser) => {
      setUser(updatedUser);
      
      // Update user query cache
      queryClient.setQueryData(queryKeys.auth.user(), updatedUser);
      
      addNotification({
        type: 'success',
        title: 'Profile updated',
        message: 'Your profile has been updated successfully.',
      });
    },
    onError: (error: any) => {
      addNotification({
        type: 'error',
        title: 'Update failed',
        message: error.message || 'Failed to update profile. Please try again.',
      });
    },
  });
}

// Change password mutation
export function useChangePassword() {
  const { addNotification } = useUIActions();

  return useMutation({
    mutationFn: ({ currentPassword, newPassword }: { currentPassword: string; newPassword: string }) =>
      AuthService.changePassword(currentPassword, newPassword),
    onSuccess: () => {
      addNotification({
        type: 'success',
        title: 'Password changed',
        message: 'Your password has been updated successfully.',
      });
    },
    onError: (error: any) => {
      addNotification({
        type: 'error',
        title: 'Password change failed',
        message: error.message || 'Failed to change password. Please try again.',
      });
    },
  });
}

// Request password reset mutation
export function useRequestPasswordReset() {
  const { addNotification } = useUIActions();

  return useMutation({
    mutationFn: (email: string) => AuthService.requestPasswordReset(email),
    onSuccess: () => {
      addNotification({
        type: 'success',
        title: 'Reset email sent',
        message: 'Please check your email for password reset instructions.',
      });
    },
    onError: (error: any) => {
      addNotification({
        type: 'error',
        title: 'Reset failed',
        message: error.message || 'Failed to send reset email. Please try again.',
      });
    },
  });
}

// Reset password mutation
export function useResetPassword() {
  const { addNotification } = useUIActions();

  return useMutation({
    mutationFn: ({ token, password }: { token: string; password: string }) =>
      AuthService.resetPassword(token, password),
    onSuccess: () => {
      addNotification({
        type: 'success',
        title: 'Password reset successful',
        message: 'Your password has been reset. You can now log in with your new password.',
      });
    },
    onError: (error: any) => {
      addNotification({
        type: 'error',
        title: 'Reset failed',
        message: error.message || 'Failed to reset password. Please try again.',
      });
    },
  });
}
