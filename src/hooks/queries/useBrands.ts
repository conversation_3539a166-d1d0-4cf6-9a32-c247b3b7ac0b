// Brand-related TanStack Query hooks
// Handles brand fetching and navigation

import { useQuery } from '@tanstack/react-query';
import { BrandService } from '../../services/api-client';
import { queryKeys } from '../../services/query-keys';

// Get all brands
export function useBrands() {
  return useQuery({
    queryKey: queryKeys.brands.list(),
    queryFn: BrandService.getBrands,
    staleTime: 30 * 60 * 1000, // 30 minutes - brands don't change often
  });
}

// Get single brand by slug
export function useBrand(slug: string, enabled = true) {
  return useQuery({
    queryKey: queryKeys.brands.detail(slug),
    queryFn: () => BrandService.getBrand(slug),
    enabled: enabled && !!slug,
    staleTime: 30 * 60 * 1000,
  });
}
