// TanStack Query keys factory for consistent caching
// Hierarchical key structure for efficient cache invalidation

import type { ProductFilters } from '../types';

export const queryKeys = {
  // Authentication
  auth: {
    all: ['auth'] as const,
    user: () => [...queryKeys.auth.all, 'user'] as const,
    profile: () => [...queryKeys.auth.all, 'profile'] as const,
  },

  // Products
  products: {
    all: ['products'] as const,
    lists: () => [...queryKeys.products.all, 'list'] as const,
    list: (filters?: ProductFilters) => [...queryKeys.products.lists(), filters] as const,
    details: () => [...queryKeys.products.all, 'detail'] as const,
    detail: (slug: string) => [...queryKeys.products.details(), slug] as const,
    search: (query: string) => [...queryKeys.products.all, 'search', query] as const,
    featured: () => [...queryKeys.products.all, 'featured'] as const,
    related: (productId: number) => [...queryKeys.products.all, 'related', productId] as const,
    recommendations: (userId?: number) => [...queryKeys.products.all, 'recommendations', userId] as const,
  },

  // Categories
  categories: {
    all: ['categories'] as const,
    lists: () => [...queryKeys.categories.all, 'list'] as const,
    list: (parentId?: number) => [...queryKeys.categories.lists(), parentId] as const,
    details: () => [...queryKeys.categories.all, 'detail'] as const,
    detail: (slug: string) => [...queryKeys.categories.details(), slug] as const,
    tree: () => [...queryKeys.categories.all, 'tree'] as const,
  },

  // Brands
  brands: {
    all: ['brands'] as const,
    lists: () => [...queryKeys.brands.all, 'list'] as const,
    list: () => [...queryKeys.brands.lists()] as const,
    details: () => [...queryKeys.brands.all, 'detail'] as const,
    detail: (slug: string) => [...queryKeys.brands.details(), slug] as const,
  },

  // Product Types
  productTypes: {
    all: ['product-types'] as const,
    lists: () => [...queryKeys.productTypes.all, 'list'] as const,
    list: () => [...queryKeys.productTypes.lists()] as const,
    details: () => [...queryKeys.productTypes.all, 'detail'] as const,
    detail: (slug: string) => [...queryKeys.productTypes.details(), slug] as const,
    attributes: (typeId: number) => [...queryKeys.productTypes.all, 'attributes', typeId] as const,
  },

  // Cart
  cart: {
    all: ['cart'] as const,
    items: () => [...queryKeys.cart.all, 'items'] as const,
    count: () => [...queryKeys.cart.all, 'count'] as const,
    total: () => [...queryKeys.cart.all, 'total'] as const,
  },

  // Orders
  orders: {
    all: ['orders'] as const,
    lists: () => [...queryKeys.orders.all, 'list'] as const,
    list: (userId?: number) => [...queryKeys.orders.lists(), userId] as const,
    details: () => [...queryKeys.orders.all, 'detail'] as const,
    detail: (orderNumber: string) => [...queryKeys.orders.details(), orderNumber] as const,
    tracking: (orderNumber: string) => [...queryKeys.orders.all, 'tracking', orderNumber] as const,
  },

  // Addresses
  addresses: {
    all: ['addresses'] as const,
    lists: () => [...queryKeys.addresses.all, 'list'] as const,
    list: (userId?: number) => [...queryKeys.addresses.lists(), userId] as const,
    details: () => [...queryKeys.addresses.all, 'detail'] as const,
    detail: (addressId: number) => [...queryKeys.addresses.details(), addressId] as const,
    default: (type: 'billing' | 'shipping') => [...queryKeys.addresses.all, 'default', type] as const,
  },

  // Reviews
  reviews: {
    all: ['reviews'] as const,
    lists: () => [...queryKeys.reviews.all, 'list'] as const,
    list: (productId?: number) => [...queryKeys.reviews.lists(), productId] as const,
    details: () => [...queryKeys.reviews.all, 'detail'] as const,
    detail: (reviewId: number) => [...queryKeys.reviews.details(), reviewId] as const,
    user: (userId: number) => [...queryKeys.reviews.all, 'user', userId] as const,
    stats: (productId: number) => [...queryKeys.reviews.all, 'stats', productId] as const,
  },

  // Wishlist
  wishlist: {
    all: ['wishlist'] as const,
    items: () => [...queryKeys.wishlist.all, 'items'] as const,
    count: () => [...queryKeys.wishlist.all, 'count'] as const,
    check: (productId: number) => [...queryKeys.wishlist.all, 'check', productId] as const,
  },

  // Payment
  payment: {
    all: ['payment'] as const,
    methods: () => [...queryKeys.payment.all, 'methods'] as const,
    intent: (orderId: string) => [...queryKeys.payment.all, 'intent', orderId] as const,
    status: (paymentId: string) => [...queryKeys.payment.all, 'status', paymentId] as const,
  },

  // Search
  search: {
    all: ['search'] as const,
    suggestions: (query: string) => [...queryKeys.search.all, 'suggestions', query] as const,
    results: (query: string, filters?: ProductFilters) => [...queryKeys.search.all, 'results', query, filters] as const,
    popular: () => [...queryKeys.search.all, 'popular'] as const,
    recent: () => [...queryKeys.search.all, 'recent'] as const,
  },

  // Filters
  filters: {
    all: ['filters'] as const,
    options: (categoryId?: number) => [...queryKeys.filters.all, 'options', categoryId] as const,
    attributes: (productTypeId: number) => [...queryKeys.filters.all, 'attributes', productTypeId] as const,
    priceRange: (categoryId?: number) => [...queryKeys.filters.all, 'price-range', categoryId] as const,
  },

  // User preferences
  preferences: {
    all: ['preferences'] as const,
    currency: () => [...queryKeys.preferences.all, 'currency'] as const,
    language: () => [...queryKeys.preferences.all, 'language'] as const,
    notifications: () => [...queryKeys.preferences.all, 'notifications'] as const,
  },

  // Analytics (for user behavior)
  analytics: {
    all: ['analytics'] as const,
    viewed: () => [...queryKeys.analytics.all, 'viewed'] as const,
    popular: () => [...queryKeys.analytics.all, 'popular'] as const,
    trending: () => [...queryKeys.analytics.all, 'trending'] as const,
  },

  // Notifications
  notifications: {
    all: ['notifications'] as const,
    list: () => [...queryKeys.notifications.all, 'list'] as const,
    unread: () => [...queryKeys.notifications.all, 'unread'] as const,
    count: () => [...queryKeys.notifications.all, 'count'] as const,
  },

  // System
  system: {
    all: ['system'] as const,
    config: () => [...queryKeys.system.all, 'config'] as const,
    health: () => [...queryKeys.system.all, 'health'] as const,
    currencies: () => [...queryKeys.system.all, 'currencies'] as const,
    countries: () => [...queryKeys.system.all, 'countries'] as const,
    shipping: () => [...queryKeys.system.all, 'shipping'] as const,
  },
} as const;

// Helper functions for query key management
export const queryKeyUtils = {
  /**
   * Get all query keys for an entity
   */
  getEntityKeys: (entity: keyof typeof queryKeys) => {
    return queryKeys[entity].all;
  },

  /**
   * Check if a query key belongs to an entity
   */
  belongsToEntity: (queryKey: readonly unknown[], entity: keyof typeof queryKeys) => {
    const entityKeys = queryKeys[entity].all;
    return queryKey.length >= entityKeys.length && 
           entityKeys.every((key, index) => queryKey[index] === key);
  },

  /**
   * Extract entity from query key
   */
  extractEntity: (queryKey: readonly unknown[]): keyof typeof queryKeys | null => {
    if (!queryKey.length) return null;
    
    const firstKey = queryKey[0] as string;
    const entities = Object.keys(queryKeys) as (keyof typeof queryKeys)[];
    
    return entities.find(entity => queryKeys[entity].all[0] === firstKey) || null;
  },

  /**
   * Create a query key matcher for invalidation
   */
  createMatcher: (entity: keyof typeof queryKeys, subKey?: string) => {
    const baseKey = queryKeys[entity].all;
    return subKey ? [...baseKey, subKey] : baseKey;
  },
};
