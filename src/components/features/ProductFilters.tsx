'use client';

// Product filters component for filtering products
// Handles category, brand, price, and other filters

import { useState } from 'react';
import { FiX, FiChevronDown, FiChevronUp } from 'react-icons/fi';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { useFilterStore } from '../../stores/filter-store';
import { useCategories } from '../../hooks/queries/useCategories';
import { useBrands } from '../../hooks/queries/useBrands';
import styles from './ProductFilters.module.scss';

interface ProductFiltersProps {
  onClose?: () => void;
}

export function ProductFilters({ onClose }: ProductFiltersProps) {
  const {
    filters,
    setFilter,
    clearFilter,
    clearAllFilters,
    hasActiveFilters,
    getActiveFilterCount,
  } = useFilterStore();

  const { data: categories } = useCategories();
  const { data: brands } = useBrands();

  const [expandedSections, setExpandedSections] = useState({
    categories: true,
    brands: true,
    price: true,
  });

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const handleCategoryChange = (categorySlug: string) => {
    if (filters.category === categorySlug) {
      clearFilter('category');
    } else {
      setFilter('category', categorySlug);
    }
  };

  const handleBrandChange = (brandSlug: string) => {
    const currentBrands = filters.brand || [];
    if (currentBrands.includes(brandSlug)) {
      setFilter('brand', currentBrands.filter(b => b !== brandSlug));
    } else {
      setFilter('brand', [...currentBrands, brandSlug]);
    }
  };

  const handlePriceChange = (type: 'min' | 'max', value: string) => {
    const numValue = value ? parseFloat(value) : undefined;
    setFilter(type === 'min' ? 'min_price' : 'max_price', numValue);
  };

  const handleSortChange = (sort: string) => {
    setFilter('sort', sort);
  };

  return (
    <div className={styles.filters}>
      {/* Header */}
      <div className={styles.header}>
        <h3 className={styles.title}>
          Filters
          {hasActiveFilters() && (
            <span className={styles.activeCount}>
              ({getActiveFilterCount()})
            </span>
          )}
        </h3>
        
        <div className={styles.headerActions}>
          {hasActiveFilters() && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAllFilters}
              className={styles.clearButton}
            >
              Clear All
            </Button>
          )}
          
          {onClose && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className={styles.closeButton}
            >
              <FiX size={18} />
            </Button>
          )}
        </div>
      </div>

      {/* Sort */}
      <div className={styles.section}>
        <h4 className={styles.sectionTitle}>Sort By</h4>
        <select
          value={filters.sort || '-created_at'}
          onChange={(e) => handleSortChange(e.target.value)}
          className={styles.sortSelect}
        >
          <option value="-created_at">Newest First</option>
          <option value="created_at">Oldest First</option>
          <option value="name">Name A-Z</option>
          <option value="-name">Name Z-A</option>
          <option value="price">Price Low to High</option>
          <option value="-price">Price High to Low</option>
        </select>
      </div>

      {/* Categories */}
      {categories && categories.length > 0 && (
        <div className={styles.section}>
          <button
            onClick={() => toggleSection('categories')}
            className={styles.sectionHeader}
          >
            <h4 className={styles.sectionTitle}>Categories</h4>
            {expandedSections.categories ? (
              <FiChevronUp size={16} />
            ) : (
              <FiChevronDown size={16} />
            )}
          </button>
          
          {expandedSections.categories && (
            <div className={styles.sectionContent}>
              {categories.map((category) => (
                <label key={category.id} className={styles.checkboxLabel}>
                  <input
                    type="radio"
                    name="category"
                    checked={filters.category === category.slug}
                    onChange={() => handleCategoryChange(category.slug)}
                    className={styles.checkbox}
                  />
                  <span className={styles.checkboxText}>{category.name}</span>
                </label>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Brands */}
      {brands && brands.length > 0 && (
        <div className={styles.section}>
          <button
            onClick={() => toggleSection('brands')}
            className={styles.sectionHeader}
          >
            <h4 className={styles.sectionTitle}>Brands</h4>
            {expandedSections.brands ? (
              <FiChevronUp size={16} />
            ) : (
              <FiChevronDown size={16} />
            )}
          </button>
          
          {expandedSections.brands && (
            <div className={styles.sectionContent}>
              {brands.slice(0, 10).map((brand) => (
                <label key={brand.id} className={styles.checkboxLabel}>
                  <input
                    type="checkbox"
                    checked={filters.brand?.includes(brand.slug) || false}
                    onChange={() => handleBrandChange(brand.slug)}
                    className={styles.checkbox}
                  />
                  <span className={styles.checkboxText}>{brand.name}</span>
                </label>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Price Range */}
      <div className={styles.section}>
        <button
          onClick={() => toggleSection('price')}
          className={styles.sectionHeader}
        >
          <h4 className={styles.sectionTitle}>Price Range</h4>
          {expandedSections.price ? (
            <FiChevronUp size={16} />
          ) : (
            <FiChevronDown size={16} />
          )}
        </button>
        
        {expandedSections.price && (
          <div className={styles.sectionContent}>
            <div className={styles.priceInputs}>
              <Input
                type="number"
                placeholder="Min"
                value={filters.min_price?.toString() || ''}
                onChange={(e) => handlePriceChange('min', e.target.value)}
                size="sm"
              />
              <span className={styles.priceSeparator}>to</span>
              <Input
                type="number"
                placeholder="Max"
                value={filters.max_price?.toString() || ''}
                onChange={(e) => handlePriceChange('max', e.target.value)}
                size="sm"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
