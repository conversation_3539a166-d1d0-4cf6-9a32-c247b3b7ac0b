// Product card component for displaying products in lists and grids
// Supports different view modes and interactive features

import Link from 'next/link';
import Image from 'next/image';
import { FiHeart, FiShoppingCart, FiStar } from 'react-icons/fi';
import { Button } from '../ui/Button';
import { useCartActions } from '../../stores/cart-store';
import { useWishlistActions, useWishlistItem } from '../../stores/wishlist-store';
import { useUIActions } from '../../stores/ui-store';
import type { Product } from '../../types';
import styles from './ProductCard.module.scss';

interface ProductCardProps {
  product: Product;
  viewMode?: 'grid' | 'list';
  className?: string;
}

export function ProductCard({ product, viewMode = 'grid', className = '' }: ProductCardProps) {
  const { addItem } = useCartActions();
  const { addItem: addToWishlist, removeItem: removeFromWishlist } = useWishlistActions();
  const { isInWishlist } = useWishlistItem(product.id);
  const { addNotification } = useUIActions();

  const primaryImage = product.images?.find(img => img.is_primary) || product.images?.[0];
  const primaryVariant = product.variants?.[0];

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (primaryVariant) {
      addItem(product, primaryVariant, 1);
      addNotification({
        type: 'success',
        title: 'Added to cart',
        message: `${product.name} has been added to your cart.`,
      });
    }
  };

  const handleToggleWishlist = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (isInWishlist) {
      removeFromWishlist(product.id);
      addNotification({
        type: 'success',
        title: 'Removed from wishlist',
        message: `${product.name} has been removed from your wishlist.`,
      });
    } else {
      addToWishlist(product);
      addNotification({
        type: 'success',
        title: 'Added to wishlist',
        message: `${product.name} has been added to your wishlist.`,
      });
    }
  };

  const cardClasses = [
    styles.productCard,
    styles[viewMode],
    className,
  ]
    .filter(Boolean)
    .join(' ');

  return (
    <article className={cardClasses}>
      <Link href={`/products/${product.slug}`} className={styles.link}>
        {/* Product Image */}
        <div className={styles.imageContainer}>
          {primaryImage ? (
            <Image
              src={primaryImage.image}
              alt={primaryImage.alt_text || product.name}
              fill
              className={styles.image}
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          ) : (
            <div className={styles.placeholder}>
              📦
            </div>
          )}
          
          {/* Wishlist Button */}
          <button
            onClick={handleToggleWishlist}
            className={`${styles.wishlistButton} ${isInWishlist ? styles.active : ''}`}
            aria-label={isInWishlist ? 'Remove from wishlist' : 'Add to wishlist'}
          >
            <FiHeart size={18} />
          </button>
          
          {/* Sale Badge */}
          {primaryVariant?.compare_at_price && (
            <div className={styles.saleBadge}>
              Sale
            </div>
          )}
        </div>

        {/* Product Info */}
        <div className={styles.info}>
          <div className={styles.category}>
            {product.category.name}
          </div>
          
          <h3 className={styles.name}>{product.name}</h3>
          
          {product.short_description && viewMode === 'list' && (
            <p className={styles.description}>
              {product.short_description}
            </p>
          )}
          
          {/* Rating */}
          {product.average_rating && (
            <div className={styles.rating}>
              <div className={styles.stars}>
                {[...Array(5)].map((_, i) => (
                  <FiStar
                    key={i}
                    size={14}
                    className={i < Math.floor(product.average_rating!) ? styles.filled : styles.empty}
                  />
                ))}
              </div>
              <span className={styles.ratingText}>
                ({product.review_count})
              </span>
            </div>
          )}
          
          {/* Price */}
          <div className={styles.priceContainer}>
            <span className={styles.price}>
              ${primaryVariant?.price || product.min_price}
            </span>
            {primaryVariant?.compare_at_price && (
              <span className={styles.comparePrice}>
                ${primaryVariant.compare_at_price}
              </span>
            )}
            {product.min_price !== product.max_price && (
              <span className={styles.priceRange}>
                - ${product.max_price}
              </span>
            )}
          </div>
          
          {/* Add to Cart Button */}
          <div className={styles.actions}>
            <Button
              variant="primary"
              size={viewMode === 'list' ? 'md' : 'sm'}
              onClick={handleAddToCart}
              disabled={!primaryVariant || primaryVariant.inventory_quantity <= 0}
              fullWidth={viewMode === 'grid'}
            >
              <FiShoppingCart size={16} />
              {primaryVariant?.inventory_quantity <= 0 ? 'Out of Stock' : 'Add to Cart'}
            </Button>
          </div>
        </div>
      </Link>
    </article>
  );
}
