@use '../../styles/variables' as *;
@use '../../styles/mixins' as *;

.filters {
  background: white;
  border-radius: $border-radius-lg;
  border: 1px solid $gray-200;
  overflow: hidden;
  height: fit-content;
  
  @include mobile-only {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: $z-modal;
    border-radius: 0;
    overflow-y: auto;
  }
}

.header {
  @include flexbox(space-between, center);
  padding: $spacing-4;
  border-bottom: 1px solid $gray-200;
  background: $gray-50;
}

.title {
  font-size: $font-size-3;
  font-weight: 600;
  color: $primary-dark-text-color;
  margin: 0;
  @include flexbox(flex-start, center);
  gap: $spacing-2;
}

.activeCount {
  background: $primary-blue;
  color: white;
  font-size: $font-size-1;
  padding: 2px 6px;
  border-radius: $border-radius-full;
  font-weight: 500;
}

.headerActions {
  @include flexbox(flex-end, center);
  gap: $spacing-2;
}

.clearButton {
  color: $primary-blue;
  font-size: $font-size-1;
}

.closeButton {
  @include laptop-up {
    display: none;
  }
}

.section {
  border-bottom: 1px solid $gray-200;
  
  &:last-child {
    border-bottom: none;
  }
}

.sectionHeader {
  @include flexbox(space-between, center);
  width: 100%;
  padding: $spacing-4;
  background: none;
  border: none;
  cursor: pointer;
  transition: background-color $transition-fast;
  
  &:hover {
    background: $gray-50;
  }
}

.sectionTitle {
  font-size: $font-size-2;
  font-weight: 600;
  color: $primary-dark-text-color;
  margin: 0;
  text-align: left;
}

.sectionContent {
  padding: 0 $spacing-4 $spacing-4;
  display: flex;
  flex-direction: column;
  gap: $spacing-3;
}

.sortSelect {
  @include input-base;
  width: 100%;
  margin: 0 $spacing-4 $spacing-4;
}

.checkboxLabel {
  @include flexbox(flex-start, center);
  gap: $spacing-2;
  cursor: pointer;
  padding: $spacing-1 0;
  
  &:hover {
    .checkboxText {
      color: $primary-blue;
    }
  }
}

.checkbox {
  width: 16px;
  height: 16px;
  accent-color: $primary-blue;
}

.checkboxText {
  font-size: $font-size-2;
  color: $primary-dark-text-color;
  transition: color $transition-fast;
}

.priceInputs {
  @include flexbox(flex-start, center);
  gap: $spacing-2;
}

.priceSeparator {
  color: $gray-500;
  font-size: $font-size-1;
  white-space: nowrap;
}
