@use '../../styles/variables' as *;
@use '../../styles/mixins' as *;

.productCard {
  @include card;
  transition: all $transition-fast;
  height: 100%;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: $box-shadow-lg;
  }
}

.link {
  display: block;
  height: 100%;
  text-decoration: none;
  color: inherit;
}

.imageContainer {
  position: relative;
  width: 100%;
  height: 250px;
  overflow: hidden;
  background-color: $gray-100;
  
  .list & {
    width: 200px;
    height: 150px;
    flex-shrink: 0;
  }
}

.image {
  object-fit: cover;
  transition: transform $transition-normal;
  
  .productCard:hover & {
    transform: scale(1.05);
  }
}

.placeholder {
  @include flexbox(center, center);
  width: 100%;
  height: 100%;
  font-size: 3rem;
  color: $gray-400;
  background-color: $gray-100;
}

.wishlistButton {
  position: absolute;
  top: $spacing-3;
  right: $spacing-3;
  @include flexbox(center, center);
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: $border-radius-full;
  color: $gray-600;
  cursor: pointer;
  transition: all $transition-fast;
  opacity: 0;
  transform: translateY(-10px);
  
  .productCard:hover & {
    opacity: 1;
    transform: translateY(0);
  }
  
  &:hover {
    background: white;
    color: $primary-red;
    transform: scale(1.1);
  }

  &.active {
    background: $primary-red;
    color: white;
    opacity: 1;
    transform: translateY(0);

    &:hover {
      background: darken($primary-red, 10%);
      color: white;
    }
  }
}

.saleBadge {
  position: absolute;
  top: $spacing-3;
  left: $spacing-3;
  background: $primary-red;
  color: white;
  padding: $spacing-1 $spacing-2;
  border-radius: $border-radius-sm;
  font-size: $font-size-1;
  font-weight: 600;
  text-transform: uppercase;
}

.info {
  padding: $spacing-4;
  display: flex;
  flex-direction: column;
  gap: $spacing-2;
  flex: 1;
  
  .list & {
    flex: 1;
    justify-content: space-between;
  }
}

.category {
  font-size: $font-size-1;
  color: $gray-500;
  text-transform: uppercase;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.name {
  font-size: $font-size-2;
  font-weight: 600;
  color: $primary-dark-text-color;
  line-height: 1.4;
  margin: 0;
  @include text-clamp(2);
  
  .list & {
    font-size: $font-size-3;
    @include text-clamp(1);
  }
}

.description {
  font-size: $font-size-1;
  color: $primary-lighter-text-color;
  line-height: 1.5;
  @include text-clamp(2);
  margin: 0;
}

.rating {
  @include flexbox(flex-start, center);
  gap: $spacing-1;
}

.stars {
  @include flexbox(flex-start, center);
  gap: 2px;
}

.filled {
  color: $primary-yellow;
  fill: currentColor;
}

.empty {
  color: $gray-300;
}

.ratingText {
  font-size: $font-size-1;
  color: $gray-500;
}

.priceContainer {
  @include flexbox(flex-start, center);
  gap: $spacing-2;
  margin-top: auto;
}

.price {
  font-size: $font-size-3;
  font-weight: 700;
  color: $primary-dark-text-color;
  
  .list & {
    font-size: $font-size-4;
  }
}

.comparePrice {
  font-size: $font-size-2;
  color: $gray-500;
  text-decoration: line-through;
}

.priceRange {
  font-size: $font-size-2;
  color: $gray-600;
}

.actions {
  margin-top: $spacing-3;
}

// List view specific styles
.list {
  .link {
    @include flexbox(flex-start, flex-start);
    gap: $spacing-4;
  }
  
  .info {
    padding: $spacing-3 $spacing-4;
  }
  
  .wishlistButton {
    opacity: 1;
    transform: translateY(0);
  }
}
