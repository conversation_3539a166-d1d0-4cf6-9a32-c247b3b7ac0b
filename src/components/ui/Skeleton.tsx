'use client'

// Skeleton loading component
// Provides animated loading placeholders for better UX

import { forwardRef } from 'react'
import styles from './Skeleton.module.scss'

interface SkeletonProps {
  width?: string | number
  height?: string | number
  variant?: 'text' | 'rectangular' | 'circular'
  animation?: 'pulse' | 'wave' | 'none'
  className?: string
  children?: React.ReactNode
}

export const Skeleton = forwardRef<HTMLDivElement, SkeletonProps>(
  ({ 
    width, 
    height, 
    variant = 'rectangular', 
    animation = 'pulse',
    className = '',
    children,
    ...props 
  }, ref) => {
    const style: React.CSSProperties = {}
    
    if (width) {
      style.width = typeof width === 'number' ? `${width}px` : width
    }
    
    if (height) {
      style.height = typeof height === 'number' ? `${height}px` : height
    }

    return (
      <div
        ref={ref}
        className={`${styles.skeleton} ${styles[variant]} ${styles[animation]} ${className}`}
        style={style}
        {...props}
      >
        {children}
      </div>
    )
  }
)

Skeleton.displayName = 'Skeleton'

// Predefined skeleton components for common use cases
export function SkeletonText({ 
  lines = 1, 
  className = '',
  ...props 
}: { 
  lines?: number
  className?: string
} & Omit<SkeletonProps, 'variant'>) {
  return (
    <div className={`${styles.textContainer} ${className}`}>
      {Array.from({ length: lines }, (_, i) => (
        <Skeleton
          key={i}
          variant="text"
          height={16}
          width={i === lines - 1 ? '75%' : '100%'}
          {...props}
        />
      ))}
    </div>
  )
}

export function SkeletonAvatar({ 
  size = 40,
  className = '',
  ...props 
}: { 
  size?: number
  className?: string
} & Omit<SkeletonProps, 'variant' | 'width' | 'height'>) {
  return (
    <Skeleton
      variant="circular"
      width={size}
      height={size}
      className={className}
      {...props}
    />
  )
}

export function SkeletonButton({ 
  width = 100,
  height = 40,
  className = '',
  ...props 
}: { 
  width?: number | string
  height?: number | string
  className?: string
} & Omit<SkeletonProps, 'variant'>) {
  return (
    <Skeleton
      variant="rectangular"
      width={width}
      height={height}
      className={`${styles.button} ${className}`}
      {...props}
    />
  )
}

export function SkeletonCard({ 
  className = '',
  ...props 
}: { 
  className?: string
} & Omit<SkeletonProps, 'variant'>) {
  return (
    <div className={`${styles.card} ${className}`}>
      <Skeleton variant="rectangular" height={200} {...props} />
      <div className={styles.cardContent}>
        <SkeletonText lines={2} />
        <div className={styles.cardFooter}>
          <Skeleton width={80} height={24} />
          <Skeleton width={60} height={32} />
        </div>
      </div>
    </div>
  )
}

export function SkeletonProductCard({ 
  className = '',
  ...props 
}: { 
  className?: string
} & Omit<SkeletonProps, 'variant'>) {
  return (
    <div className={`${styles.productCard} ${className}`}>
      <Skeleton variant="rectangular" height={250} className={styles.productImage} {...props} />
      <div className={styles.productInfo}>
        <SkeletonText lines={2} />
        <div className={styles.productMeta}>
          <Skeleton width={60} height={20} />
          <Skeleton width={80} height={24} />
        </div>
        <div className={styles.productActions}>
          <SkeletonButton width="100%" height={40} />
        </div>
      </div>
    </div>
  )
}

export function SkeletonList({ 
  items = 5,
  className = '',
  ...props 
}: { 
  items?: number
  className?: string
} & Omit<SkeletonProps, 'variant'>) {
  return (
    <div className={`${styles.list} ${className}`}>
      {Array.from({ length: items }, (_, i) => (
        <div key={i} className={styles.listItem}>
          <SkeletonAvatar size={48} />
          <div className={styles.listContent}>
            <SkeletonText lines={2} />
          </div>
          <Skeleton width={60} height={20} />
        </div>
      ))}
    </div>
  )
}

export function SkeletonTable({ 
  rows = 5,
  columns = 4,
  className = '',
  ...props 
}: { 
  rows?: number
  columns?: number
  className?: string
} & Omit<SkeletonProps, 'variant'>) {
  return (
    <div className={`${styles.table} ${className}`}>
      {/* Header */}
      <div className={styles.tableHeader}>
        {Array.from({ length: columns }, (_, i) => (
          <Skeleton key={i} height={20} width="80%" />
        ))}
      </div>
      
      {/* Rows */}
      {Array.from({ length: rows }, (_, rowIndex) => (
        <div key={rowIndex} className={styles.tableRow}>
          {Array.from({ length: columns }, (_, colIndex) => (
            <Skeleton key={colIndex} height={16} width="90%" />
          ))}
        </div>
      ))}
    </div>
  )
}
