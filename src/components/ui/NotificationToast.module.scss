@use '../../styles/mixins.scss' as *;
@use '../../styles/variables.scss' as *;

.toast {
  @include flexbox(flex-start, flex-start);
  @include card;

  gap: $spacing-3;
  padding: $spacing-4;
  min-width: 300px;
  max-width: 400px;
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s ease-in-out;

  @include mobile-only {
    min-width: auto;
    max-width: none;
  }

  &.visible {
    transform: translateX(0);
    opacity: 1;
  }

  &.exiting {
    transform: translateX(100%);
    opacity: 0;
  }
}

.iconContainer {
  @include flexbox(center, center);
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  border-radius: $border-radius-full;
}

.icon {
  color: white;
}

.content {
  flex: 1;
  min-width: 0;
}

.title {
  font-weight: 600;
  font-size: $font-size-2;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-1;
}

.message {
  font-size: $font-size-1;
  color: $primary-lighter-text-color;
  line-height: 1.4;
}

.actions {
  margin-top: $spacing-2;
}

.actionButton {
  padding: 0;
  font-size: $font-size-1;
}

.closeButton {
  flex-shrink: 0;
  padding: $spacing-1;
  margin: -#{$spacing-1} -#{$spacing-1} -#{$spacing-1} 0;
  color: $gray-500;

  &:hover {
    color: $primary-dark-text-color;
  }
}

// Type variants
.success {
  border-left: 4px solid $success;

  .iconContainer {
    background-color: $success;
  }
}

.error {
  border-left: 4px solid $error;

  .iconContainer {
    background-color: $error;
  }
}

.warning {
  border-left: 4px solid $warning;

  .iconContainer {
    background-color: $warning;
  }

  .icon {
    // color: $warning-text;
  }
}

.info {
  border-left: 4px solid $info;

  .iconContainer {
    background-color: $info;
  }
}