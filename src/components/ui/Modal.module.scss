@use '../../styles/variables' as *;
@use '../../styles/mixins' as *;

.backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: $z-modal-backdrop;
  
  @include flexbox(center, center);
  
  padding: $spacing-4;
  overflow-y: auto;
}

.modal {
  background: white;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow-lg;
  z-index: $z-modal;
  max-height: 90vh;
  overflow-y: auto;
  
  @include mobile-only {
    width: 100%;
    max-height: 95vh;
    margin: $spacing-2;
  }
  
  &:focus {
    outline: none;
  }
}

// Sizes
.sm {
  width: 100%;
  max-width: 400px;
}

.md {
  width: 100%;
  max-width: 500px;
}

.lg {
  width: 100%;
  max-width: 700px;
}

.xl {
  width: 100%;
  max-width: 900px;
}

.full {
  width: 95vw;
  height: 95vh;
  max-width: none;
  max-height: none;
}

.header {
  @include flexbox(space-between, center);
  padding: $spacing-6 $spacing-6 $spacing-4;
  border-bottom: 1px solid $gray-200;
}

.title {
  font-size: $font-size-4;
  font-weight: 600;
  color: $primary-dark-text-color;
  margin: 0;
}

.closeButton {
  margin-left: $spacing-4;
  flex-shrink: 0;
}

.content {
  padding: $spacing-6;
  
  .modal.full & {
    padding: $spacing-8;
    height: calc(100% - 80px); // Account for header
    overflow-y: auto;
  }
}
