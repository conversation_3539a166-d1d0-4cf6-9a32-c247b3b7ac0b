@use '../../styles/variables' as *;
@use '../../styles/mixins' as *;

.spinner {
  @include flexbox(center, center);
}

.circle {
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: $border-radius-full;
  animation: spin 1s linear infinite;
}

.sm .circle {
  width: 16px;
  height: 16px;
}

.md .circle {
  width: 24px;
  height: 24px;
}

.lg .circle {
  width: 32px;
  height: 32px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
