@use '../../styles/variables.scss' as *;
@use '../../styles/mixins' as *;

.container {
  display: flex;
  flex-direction: column;
  gap: $spacing-1;
}

.fullWidth {
  width: 100%;
}

.label {
  font-size: $font-size-1;
  font-weight: 500;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-1;
}

.inputWrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input {
  @include input-base;

  &.sm {
    padding: $spacing-2 $spacing-3;
    font-size: $font-size-1;
  }

  &.md {
    padding: $spacing-3 $spacing-4;
    font-size: $font-size-2;
  }

  &.lg {
    padding: $spacing-4 $spacing-5;
    font-size: $font-size-3;
  }

  &.hasLeftIcon {
    &.sm {
      padding-left: calc(#{$spacing-2} + 20px + #{$spacing-2});
    }

    &.md {
      padding-left: calc(#{$spacing-3} + 24px + #{$spacing-2});
    }

    &.lg {
      padding-left: calc(#{$spacing-4} + 28px + #{$spacing-2});
    }
  }

  &.hasRightIcon {
    &.sm {
      padding-right: calc(#{$spacing-2} + 20px + #{$spacing-2});
    }

    &.md {
      padding-right: calc(#{$spacing-3} + 24px + #{$spacing-2});
    }

    &.lg {
      padding-right: calc(#{$spacing-4} + 28px + #{$spacing-2});
    }
  }
}

.leftIcon,
.rightIcon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  color: $gray-500;
  pointer-events: none;

  @include flexbox(center, center);
}

.leftIcon {
  left: $spacing-3;
}

.rightIcon {
  right: $spacing-3;
}

.helperText {
  font-size: $font-size-1;
  margin-top: $spacing-1;
}

.errorText {
  color: $error;
}

.helper {
  color: $gray-500;
}