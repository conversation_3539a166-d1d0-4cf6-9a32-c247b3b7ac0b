'use client'

// Enhanced error boundary component
// Provides graceful error handling with recovery options

import React, { Component, ErrorInfo, ReactNode } from 'react'
import { FiAlertTriangle, FiRefreshCw, FiHome, FiMail } from 'react-icons/fi'
import { Button } from './Button'
import styles from './ErrorBoundary.module.scss'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
  showDetails?: boolean
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
  eventId: string | null
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      eventId: null,
    }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('ErrorBoundary caught an error:', error, errorInfo)
    }

    // Generate a unique event ID for error tracking
    const eventId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    this.setState({
      error,
      errorInfo,
      eventId,
    })

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }

    // In production, you would send this to your error tracking service
    // Example: Sentry.captureException(error, { contexts: { react: errorInfo } })
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      eventId: null,
    })
  }

  handleReload = () => {
    window.location.reload()
  }

  handleGoHome = () => {
    window.location.href = '/'
  }

  handleReportError = () => {
    const { error, errorInfo, eventId } = this.state
    const errorReport = {
      eventId,
      error: error?.toString(),
      stack: error?.stack,
      componentStack: errorInfo?.componentStack,
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date().toISOString(),
    }

    // Create mailto link with error details
    const subject = encodeURIComponent(`Error Report - ${eventId}`)
    const body = encodeURIComponent(`
Error Report

Event ID: ${eventId}
URL: ${window.location.href}
Timestamp: ${new Date().toISOString()}

Error: ${error?.toString()}

Please describe what you were doing when this error occurred:
[Your description here]

Technical Details:
${JSON.stringify(errorReport, null, 2)}
    `)

    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`)
  }

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback
      }

      const { error, errorInfo, eventId } = this.state
      const isDevelopment = process.env.NODE_ENV === 'development'

      return (
        <div className={styles.errorBoundary}>
          <div className={styles.errorContainer}>
            <div className={styles.errorIcon}>
              <FiAlertTriangle size={48} />
            </div>

            <div className={styles.errorContent}>
              <h1 className={styles.errorTitle}>Oops! Something went wrong</h1>
              <p className={styles.errorMessage}>
                We're sorry, but something unexpected happened. Our team has been notified.
              </p>

              {eventId && (
                <div className={styles.errorId}>
                  <strong>Error ID:</strong> {eventId}
                </div>
              )}

              <div className={styles.errorActions}>
                <Button
                  variant="primary"
                  leftIcon={<FiRefreshCw size={18} />}
                  onClick={this.handleRetry}
                >
                  Try Again
                </Button>

                <Button
                  variant="secondary"
                  leftIcon={<FiHome size={18} />}
                  onClick={this.handleGoHome}
                >
                  Go Home
                </Button>

                <Button
                  variant="ghost"
                  leftIcon={<FiMail size={18} />}
                  onClick={this.handleReportError}
                >
                  Report Issue
                </Button>
              </div>

              {(isDevelopment || this.props.showDetails) && error && (
                <details className={styles.errorDetails}>
                  <summary>Technical Details</summary>
                  <div className={styles.errorStack}>
                    <h4>Error:</h4>
                    <pre>{error.toString()}</pre>
                    
                    {error.stack && (
                      <>
                        <h4>Stack Trace:</h4>
                        <pre>{error.stack}</pre>
                      </>
                    )}
                    
                    {errorInfo?.componentStack && (
                      <>
                        <h4>Component Stack:</h4>
                        <pre>{errorInfo.componentStack}</pre>
                      </>
                    )}
                  </div>
                </details>
              )}
            </div>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

// HOC for wrapping components with error boundary
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  )

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`

  return WrappedComponent
}

// Hook for error boundary in functional components
export function useErrorHandler() {
  return (error: Error, errorInfo?: ErrorInfo) => {
    // In a real app, you might want to throw the error to trigger the error boundary
    // or send it to an error tracking service
    console.error('Error caught by useErrorHandler:', error, errorInfo)
    
    // You could also trigger a global error state here
    // Example: useErrorStore.getState().setError(error)
  }
}
