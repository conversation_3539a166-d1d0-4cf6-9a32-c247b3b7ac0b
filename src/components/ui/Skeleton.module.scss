@use '../../styles/variables' as *;
@use '../../styles/mixins' as *;

// Base skeleton styles
.skeleton {
  background: linear-gradient(90deg, $gray-200 25%, $gray-100 50%, $gray-200 75%);
  background-size: 200% 100%;
  border-radius: $border-radius-sm;
  display: inline-block;
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: inherit;
  }
}

// Skeleton variants
.rectangular {
  border-radius: $border-radius-md;
}

.circular {
  border-radius: $border-radius-full;
}

.text {
  border-radius: $border-radius-sm;
  height: 1em;
  margin-bottom: $spacing-1;

  &:last-child {
    margin-bottom: 0;
  }
}

// Animation variants
.pulse {
  animation: skeletonPulse 1.5s ease-in-out infinite;
}

.wave {
  animation: skeletonWave 1.5s linear infinite;
}

.none {
  animation: none;
}

// Animations
@keyframes skeletonPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

@keyframes skeletonWave {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

// Predefined component styles
.textContainer {
  display: flex;
  flex-direction: column;
  gap: $spacing-1;
}

.button {
  border-radius: $border-radius-md;
}

.card {
  border: 1px solid $gray-200;
  border-radius: $border-radius-lg;
  overflow: hidden;
  background: white;
}

.cardContent {
  padding: $spacing-4;
}

.cardFooter {
  @include flexbox(space-between, center);
  margin-top: $spacing-3;
}

.productCard {
  border: 1px solid $gray-200;
  border-radius: $border-radius-lg;
  overflow: hidden;
  background: white;
}

.productImage {
  width: 100%;
}

.productInfo {
  padding: $spacing-4;
}

.productMeta {
  @include flexbox(space-between, center);
  margin: $spacing-3 0;
}

.productActions {
  margin-top: $spacing-4;
}

.list {
  display: flex;
  flex-direction: column;
  gap: $spacing-3;
}

.listItem {
  @include flexbox(flex-start, center);
  gap: $spacing-3;
  padding: $spacing-3;
  border: 1px solid $gray-200;
  border-radius: $border-radius-md;
  background: white;
}

.listContent {
  flex: 1;
}

.table {
  border: 1px solid $gray-200;
  border-radius: $border-radius-md;
  overflow: hidden;
  background: white;
}

.tableHeader {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: $spacing-3;
  padding: $spacing-4;
  background: $gray-50;
  border-bottom: 1px solid $gray-200;
}

.tableRow {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: $spacing-3;
  padding: $spacing-3 $spacing-4;
  border-bottom: 1px solid $gray-100;

  &:last-child {
    border-bottom: none;
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .skeleton {
    background: linear-gradient(90deg, #374151 25%, #4B5563 50%, #374151 75%);
    background-size: 200% 100%;
  }

  .card,
  .productCard,
  .listItem,
  .table {
    background: #1F2937;
    border-color: #374151;
  }

  .tableHeader {
    background: #374151;
    border-color: #4B5563;
  }

  .tableRow {
    border-color: #374151;
  }
}

// Responsive adjustments
@include mobile-only {
  .cardContent,
  .productInfo {
    padding: $spacing-3;
  }

  .listItem {
    padding: $spacing-2;
  }

  .tableHeader,
  .tableRow {
    padding: $spacing-2 $spacing-3;
    gap: $spacing-2;
  }
}
