@use '../../styles/variables' as *;
@use '../../styles/mixins' as *;

.header {
  background: white;
  border-bottom: 1px solid $gray-200;
  position: sticky;
  top: 0;
  // z-index: $z-sticky;
}

.container {
  @include container;
  @include flexbox(space-between, center);
  height: 64px;
  gap: $spacing-4;
}

.mobileMenuButton {
  @include laptop-up {
    display: none;
  }
}

.logo {
  @include flexbox(center, center);
  text-decoration: none;
  color: $primary-blue;
  font-weight: 700;
  font-size: $font-size-4;

  @include mobile-only {
    font-size: $font-size-3;
  }
}

.logoText {
  white-space: nowrap;
}

.desktopNav {
  @include flexbox(center, center);
  gap: $spacing-6;

  @media (max-width: #{$laptop - 1px}) {
    display: none;
  }
}

.navLink {
  color: $primary-dark-text-color;
  text-decoration: none;
  font-weight: 500;
  padding: $spacing-2 0;
  transition: color $transition-fast;

  &:hover {
    color: $primary-blue;
  }
}

.searchContainer {
  flex: 1;
  max-width: 500px;

  @media (max-width: #{$tablet - 1px}) {
    display: none;
  }
}

.actions {
  @include flexbox(center, center);
  gap: $spacing-2;
}

.mobileSearchButton {
  @include tablet-up {
    display: none;
  }
}

.actionButton {
  @include flexbox(center, center);
  gap: $spacing-1;
  padding: $spacing-2 $spacing-3;
  color: $primary-dark-text-color;
  text-decoration: none;
  border-radius: $border-radius-md;
  transition: background-color $transition-fast;

  &:hover {
    background-color: $gray-100;
  }

  @include mobile-only {
    .actionLabel {
      display: none;
    }
  }
}

.actionLabel {
  font-size: $font-size-1;
  font-weight: 500;
}

.authButtons {
  @include flexbox(center, center);
  gap: $spacing-2;

  @include mobile-only {
    display: none;
  }
}

.authButtonText {
  @include mobile-only {
    display: none;
  }
}

.mobileSearch {
  background: white;
  border-bottom: 1px solid $gray-200;
  padding: $spacing-3 $spacing-4;

  @include tablet-up {
    display: none;
  }
}

.mobileNav {
  background: white;
  border-bottom: 1px solid $gray-200;
  padding: $spacing-4;

  @include laptop-up {
    display: none;
  }
}

.mobileNavLink {
  display: block;
  color: $primary-dark-text-color;
  text-decoration: none;
  font-weight: 500;
  padding: $spacing-3 0;
  border-bottom: 1px solid $gray-100;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    color: $primary-blue;
  }
}

.mobileAuthButtons {
  @include flexbox(center, center, column);
  gap: $spacing-3;
  margin-top: $spacing-4;
  padding-top: $spacing-4;
  border-top: 1px solid $gray-200;
}