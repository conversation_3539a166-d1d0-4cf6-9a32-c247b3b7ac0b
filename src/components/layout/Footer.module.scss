@use '../../styles/variables' as *;
@use '../../styles/mixins' as *;


.footer {
  background-color: $primary-dark;
  color: white;
  margin-top: auto;
}

.container {
  @include container;
  padding-top: $spacing-16;
  padding-bottom: $spacing-8;
}

.content {
  // @include grid-responsive(1, 2, 4, 4);
  gap: $spacing-8;
  margin-bottom: $spacing-12;

  @include mobile-only {
    gap: $spacing-6;
    margin-bottom: $spacing-8;
  }
}

.section {
  display: flex;
  flex-direction: column;
  gap: $spacing-4;
}

.sectionTitle {
  font-size: $font-size-3;
  font-weight: 600;
  color: white;
  margin: 0;
}

.description {
  color: $gray-300;
  line-height: 1.6;
  margin: 0;
}

.socialLinks {
  @include flexbox(flex-start, center);
  gap: $spacing-3;
  margin-top: $spacing-2;
}

.socialLink {
  @include flexbox(center, center);
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: $border-radius-full;
  text-decoration: none;
  transition: all $transition-fast;

  &:hover {
    background-color: $primary-blue;
    transform: translateY(-2px);
  }
}

.linkList {
  display: flex;
  flex-direction: column;
  gap: $spacing-2;
}

.link {
  color: $gray-300;
  text-decoration: none;
  font-size: $font-size-2;
  transition: color $transition-fast;

  &:hover {
    color: white;
  }
}

.bottomBar {
  @include flexbox(space-between, center);
  padding-top: $spacing-6;
  border-top: 1px solid rgba(255, 255, 255, 0.1);

  @include mobile-only {
    flex-direction: column;
    gap: $spacing-4;
    text-align: center;
  }
}

.copyright {
  color: $gray-400;
  font-size: $font-size-1;
}

.paymentMethods {
  @include flexbox(center, center);
  gap: $spacing-3;
}

.paymentText {
  color: $gray-400;
  font-size: $font-size-1;
}

.paymentIcons {
  @include flexbox(center, center);
  gap: $spacing-2;
}

.paymentIcon {
  font-size: $font-size-4;
  opacity: 0.8;
}