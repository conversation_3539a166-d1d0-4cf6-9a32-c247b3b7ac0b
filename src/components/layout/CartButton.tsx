'use client';

// <PERSON>t button with quantity badge and cart sidebar toggle
// Shows cart item count and provides quick access to cart

import Link from 'next/link';
import { FiShoppingCart } from 'react-icons/fi';
import { Button } from '../ui/Button';
import { useTotalQuantity } from '../../stores/cart-store';
import { useUIActions } from '../../stores/ui-store';
import styles from './CartButton.module.scss';

export function CartButton() {
  const totalQuantity = useTotalQuantity();
  const { toggleCartSidebar } = useUIActions();

  const handleCartClick = () => {
    // For mobile, toggle sidebar; for desktop, go to cart page
    if (window.innerWidth < 768) {
      toggleCartSidebar();
    }
  };

  return (
    <div className={styles.cartButton}>
      <Link href="/cart" className={styles.link}>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCartClick}
          className={styles.button}
          aria-label={`Shopping cart with ${totalQuantity} items`}
        >
          <div className={styles.iconContainer}>
            <FiShoppingCart size={20} />
            {totalQuantity > 0 && (
              <span className={styles.badge}>
                {totalQuantity > 99 ? '99+' : totalQuantity}
              </span>
            )}
          </div>
          <span className={styles.label}>Cart</span>
        </Button>
      </Link>
    </div>
  );
}
