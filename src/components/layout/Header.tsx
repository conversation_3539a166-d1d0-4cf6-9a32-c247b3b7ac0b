'use client';

// Main header component with navigation, search, and user menu
// Responsive design with mobile menu support

import Link from 'next/link';
import { FiMenu, FiSearch, FiShoppingCart, FiUser, FiHeart } from 'react-icons/fi';
import { Button } from '../ui/Button';
import { SearchBar } from './SearchBar';
import { UserMenu } from './UserMenu';
import { CartButton } from './CartButton';
import { useUIActions, useMobileMenuOpen, useSearchOpen } from '../../stores/ui-store';
import { useIsAuthenticated } from '../../stores/auth-store';
import { useTotalQuantity } from '../../stores/cart-store';
import styles from './Header.module.scss';

export function Header() {
  const { toggleMobileMenu, toggleSearch, setSearchOpen } = useUIActions();
  const mobileMenuOpen = useMobileMenuOpen();
  const searchOpen = useSearchOpen();
  const isAuthenticated = useIsAuthenticated();
  const cartQuantity = useTotalQuantity();

  return (
    <header className={styles.header}>
      <div className={styles.container}>
        {/* Mobile Menu Button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleMobileMenu}
          className={styles.mobileMenuButton}
          aria-label="Toggle mobile menu"
        >
          <FiMenu size={20} />
        </Button>

        {/* Logo */}
        <Link href="/" className={styles.logo}>
          <span className={styles.logoText}>Picky Store</span>
        </Link>

        {/* Desktop Navigation */}
        <nav className={styles.desktopNav}>
          <Link href="/products" className={styles.navLink}>
            Products
          </Link>
          <Link href="/categories" className={styles.navLink}>
            Categories
          </Link>
          <Link href="/brands" className={styles.navLink}>
            Brands
          </Link>
          <Link href="/deals" className={styles.navLink}>
            Deals
          </Link>
        </nav>

        {/* Search Bar - Desktop */}
        <div className={styles.searchContainer}>
          <SearchBar />
        </div>

        {/* Actions */}
        <div className={styles.actions}>
          {/* Mobile Search Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleSearch}
            className={styles.mobileSearchButton}
            aria-label="Toggle search"
          >
            <FiSearch size={20} />
          </Button>

          {/* Wishlist */}
          {isAuthenticated && (
            <Link href="/wishlist" className={styles.actionButton}>
              <FiHeart size={20} />
              <span className={styles.actionLabel}>Wishlist</span>
            </Link>
          )}

          {/* Cart */}
          <CartButton />

          {/* User Menu */}
          {isAuthenticated ? (
            <UserMenu />
          ) : (
            <div className={styles.authButtons}>
              <Link href="/auth/login">
                <Button variant="ghost" size="sm">
                  <FiUser size={16} />
                  <span className={styles.authButtonText}>Sign In</span>
                </Button>
              </Link>
              <Link href="/auth/register">
                <Button variant="primary" size="sm">
                  Sign Up
                </Button>
              </Link>
            </div>
          )}
        </div>
      </div>

      {/* Mobile Search Bar */}
      {searchOpen && (
        <div className={styles.mobileSearch}>
          <SearchBar 
            autoFocus 
            onClose={() => setSearchOpen(false)}
            placeholder="Search products..."
          />
        </div>
      )}

      {/* Mobile Navigation */}
      {mobileMenuOpen && (
        <nav className={styles.mobileNav}>
          <Link href="/products" className={styles.mobileNavLink}>
            Products
          </Link>
          <Link href="/categories" className={styles.mobileNavLink}>
            Categories
          </Link>
          <Link href="/brands" className={styles.mobileNavLink}>
            Brands
          </Link>
          <Link href="/deals" className={styles.mobileNavLink}>
            Deals
          </Link>
          
          {!isAuthenticated && (
            <div className={styles.mobileAuthButtons}>
              <Link href="/auth/login">
                <Button variant="secondary" fullWidth>
                  Sign In
                </Button>
              </Link>
              <Link href="/auth/register">
                <Button variant="primary" fullWidth>
                  Sign Up
                </Button>
              </Link>
            </div>
          )}
        </nav>
      )}
    </header>
  );
}
