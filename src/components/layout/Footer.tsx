// Footer component with links and company information
// Responsive design with organized sections

import Link from 'next/link';
import { FiFacebook, FiTwitter, FiInstagram, FiMail } from 'react-icons/fi';
import styles from './Footer.module.scss';

export function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className={styles.footer}>
      <div className={styles.container}>
        <div className={styles.content}>
          {/* Company Info */}
          <div className={styles.section}>
            <h3 className={styles.sectionTitle}>Picky Store</h3>
            <p className={styles.description}>
              Your trusted e-commerce destination for quality products and exceptional service.
            </p>
            <div className={styles.socialLinks}>
              <a href="#" className={styles.socialLink} aria-label="Facebook">
                <FiFacebook size={20} />
              </a>
              <a href="#" className={styles.socialLink} aria-label="Twitter">
                <FiTwitter size={20} />
              </a>
              <a href="#" className={styles.socialLink} aria-label="Instagram">
                <FiInstagram size={20} />
              </a>
              <a href="mailto:<EMAIL>" className={styles.socialLink} aria-label="Email">
                <FiMail size={20} />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div className={styles.section}>
            <h4 className={styles.sectionTitle}>Quick Links</h4>
            <nav className={styles.linkList}>
              <Link href="/products" className={styles.link}>Products</Link>
              <Link href="/categories" className={styles.link}>Categories</Link>
              <Link href="/brands" className={styles.link}>Brands</Link>
              <Link href="/deals" className={styles.link}>Deals</Link>
              <Link href="/about" className={styles.link}>About Us</Link>
            </nav>
          </div>

          {/* Customer Service */}
          <div className={styles.section}>
            <h4 className={styles.sectionTitle}>Customer Service</h4>
            <nav className={styles.linkList}>
              <Link href="/contact" className={styles.link}>Contact Us</Link>
              <Link href="/help" className={styles.link}>Help Center</Link>
              <Link href="/shipping" className={styles.link}>Shipping Info</Link>
              <Link href="/returns" className={styles.link}>Returns</Link>
              <Link href="/track-order" className={styles.link}>Track Order</Link>
            </nav>
          </div>

          {/* Legal */}
          <div className={styles.section}>
            <h4 className={styles.sectionTitle}>Legal</h4>
            <nav className={styles.linkList}>
              <Link href="/privacy" className={styles.link}>Privacy Policy</Link>
              <Link href="/terms" className={styles.link}>Terms of Service</Link>
              <Link href="/cookies" className={styles.link}>Cookie Policy</Link>
              <Link href="/accessibility" className={styles.link}>Accessibility</Link>
            </nav>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className={styles.bottomBar}>
          <div className={styles.copyright}>
            © {currentYear} Picky Store. All rights reserved.
          </div>
          <div className={styles.paymentMethods}>
            <span className={styles.paymentText}>We accept:</span>
            <div className={styles.paymentIcons}>
              <span className={styles.paymentIcon}>💳</span>
              <span className={styles.paymentIcon}>🏦</span>
              <span className={styles.paymentIcon}>📱</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
