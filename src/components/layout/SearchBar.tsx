'use client';

// Search bar component with suggestions and history
// Handles search input and navigation

import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { FiSearch, FiX } from 'react-icons/fi';
import { Input } from '../ui/Input';
import { Button } from '../ui/Button';
import { useSearchQuery, useUIActions } from '../../stores/ui-store';
import styles from './SearchBar.module.scss';

interface SearchBarProps {
  autoFocus?: boolean;
  onClose?: () => void;
  placeholder?: string;
  className?: string;
}

export function SearchBar({ 
  autoFocus = false, 
  onClose, 
  placeholder = "Search products...",
  className = '' 
}: SearchBarProps) {
  const router = useRouter();
  const inputRef = useRef<HTMLInputElement>(null);
  const [query, setQuery] = useState('');
  const { setSearchQuery, addToSearchHistory } = useUIActions();

  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      setSearchQuery(query);
      addToSearchHistory(query);
      router.push(`/search?q=${encodeURIComponent(query.trim())}`);
      if (onClose) {
        onClose();
      }
    }
  };

  const handleClear = () => {
    setQuery('');
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  const containerClasses = [styles.searchBar, className]
    .filter(Boolean)
    .join(' ');

  return (
    <form onSubmit={handleSubmit} className={containerClasses}>
      <Input
        ref={inputRef}
        type="text"
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder={placeholder}
        leftIcon={<FiSearch size={18} />}
        rightIcon={
          query && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={handleClear}
              className={styles.clearButton}
            >
              <FiX size={16} />
            </Button>
          )
        }
        className={styles.searchInput}
      />
      
      {onClose && (
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={onClose}
          className={styles.closeButton}
        >
          Cancel
        </Button>
      )}
    </form>
  );
}
