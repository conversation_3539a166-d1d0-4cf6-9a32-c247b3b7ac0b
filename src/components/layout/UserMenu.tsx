'use client';

// User menu dropdown with profile and account options
// Handles user authentication state and logout

import { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { FiUser, FiSettings, FiPackage, FiHeart, FiLogOut, FiChevronDown } from 'react-icons/fi';
import { Button } from '../ui/Button';
import { useUser, useFullName, useInitials } from '../../stores/auth-store';
import { AuthService } from '../../services/api-client';
import { useUIActions } from '../../stores/ui-store';
import styles from './UserMenu.module.scss';

export function UserMenu() {
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const user = useUser();
  const fullName = useFullName();
  const initials = useInitials();
  const { addNotification } = useUIActions();

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const handleLogout = async () => {
    try {
      await AuthService.logout();
      addNotification({
        type: 'success',
        title: 'Logged out successfully',
      });
      // Redirect will be handled by auth state change
      window.location.href = '/';
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Logout failed',
        message: 'Please try again',
      });
    }
  };

  const toggleMenu = () => setIsOpen(!isOpen);

  return (
    <div className={styles.userMenu} ref={menuRef}>
      <Button
        variant="ghost"
        size="sm"
        onClick={toggleMenu}
        className={styles.trigger}
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <div className={styles.avatar}>
          {initials || <FiUser size={16} />}
        </div>
        <span className={styles.userName}>{fullName || 'User'}</span>
        <FiChevronDown 
          size={16} 
          className={`${styles.chevron} ${isOpen ? styles.open : ''}`} 
        />
      </Button>

      {isOpen && (
        <div className={styles.dropdown}>
          <div className={styles.userInfo}>
            <div className={styles.avatarLarge}>
              {initials || <FiUser size={20} />}
            </div>
            <div className={styles.userDetails}>
              <div className={styles.name}>{fullName}</div>
              <div className={styles.email}>{user?.email}</div>
            </div>
          </div>

          <div className={styles.divider} />

          <nav className={styles.menu}>
            <Link href="/account" className={styles.menuItem} onClick={() => setIsOpen(false)}>
              <FiUser size={16} />
              <span>My Account</span>
            </Link>
            
            <Link href="/account/orders" className={styles.menuItem} onClick={() => setIsOpen(false)}>
              <FiPackage size={16} />
              <span>My Orders</span>
            </Link>
            
            <Link href="/wishlist" className={styles.menuItem} onClick={() => setIsOpen(false)}>
              <FiHeart size={16} />
              <span>Wishlist</span>
            </Link>
            
            <Link href="/account/settings" className={styles.menuItem} onClick={() => setIsOpen(false)}>
              <FiSettings size={16} />
              <span>Settings</span>
            </Link>
          </nav>

          <div className={styles.divider} />

          <button onClick={handleLogout} className={styles.logoutButton}>
            <FiLogOut size={16} />
            <span>Sign Out</span>
          </button>
        </div>
      )}
    </div>
  );
}
