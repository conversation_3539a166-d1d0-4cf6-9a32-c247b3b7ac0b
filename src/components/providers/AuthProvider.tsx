'use client';

// Authentication provider that initializes auth state on app load
// <PERSON>les automatic authentication check and token refresh

import { useEffect } from 'react';
import { useAuthStore } from '../../stores/auth-store';
import { AuthService } from '../../services/api-client';

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const { setUser, setLoading, setError, setAuthenticated } = useAuthStore();

  useEffect(() => {
    const initializeAuth = async () => {
      setLoading(true);
      setError(null);

      try {
        // Try to get current user (will use HTTP-only cookies)
        const user = await AuthService.getCurrentUser();
        setUser(user);
        setAuthenticated(true);
      } catch (error: any) {
        // If getCurrentUser fails, user is not authenticated
        // This is expected for unauthenticated users, so don't set error
        setUser(null);
        setAuthenticated(false);
        
        // Only log error if it's not a 401 (unauthorized)
        if (error?.status !== 401) {
          console.error('Auth initialization error:', error);
          setError('Failed to initialize authentication');
        }
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, [setUser, setLoading, setError, setAuthenticated]);

  return <>{children}</>;
}
