'use client'

// Notification provider that renders toast notifications
// Uses the UI store for notification state management

import { useNotifications, useUIActions } from '../../stores/ui-store'
import { NotificationToast } from '../ui/NotificationToast'
import styles from './NotificationProvider.module.scss'

interface NotificationProviderProps {
  children: React.ReactNode
}

export function NotificationProvider({ children }: NotificationProviderProps) {
  const notifications = useNotifications()
  const { removeNotification } = useUIActions()

  return (
    <>
      {children}

      {/* Notification Container */}
      <div className={styles.notificationContainer}>
        {notifications.map((notification) => (
          <NotificationToast
            key={notification.id}
            notification={notification}
            onClose={() => removeNotification(notification.id)}
          />
        ))}
      </div>
    </>
  )
}
