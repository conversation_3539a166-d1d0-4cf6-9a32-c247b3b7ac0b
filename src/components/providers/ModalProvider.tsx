'use client'

// Modal provider that renders modal components
// Uses the UI store for modal state management

import { useModals, useUIActions } from '../../stores/ui-store'
import { Modal } from '../ui/Modal'

interface ModalProviderProps {
  children: React.ReactNode
}

export function ModalProvider({ children }: ModalProviderProps) {
  const modals = useModals()
  const { closeModal } = useUIActions()

  return (
    <>
      {children}

      {/* Render all active modals */}
      {modals.map((modal) => (
        <Modal
          key={modal.id}
          isOpen={true}
          onClose={() => closeModal(modal.id)}
          size={modal.size}
          closable={modal.closable}
        >
          <modal.component {...modal.props} />
        </Modal>
      ))}
    </>
  )
}
