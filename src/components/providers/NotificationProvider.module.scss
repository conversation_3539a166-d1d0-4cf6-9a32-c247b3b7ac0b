@use '../../styles/variables.scss' as *;

.notificationContainer {
  position: fixed;
  top: $spacing-4;
  right: $spacing-4;
  // z-index: $z-toast;
  display: flex;
  flex-direction: column;
  gap: $spacing-3;
  max-width: 400px;
  width: 100%;
  pointer-events: none;

  // @include mobile-only {
  //   top: $spacing-3;
  //   right: $spacing-3;
  //   left: $spacing-3;
  //   max-width: none;
  // }

  >* {
    pointer-events: auto;
  }
}