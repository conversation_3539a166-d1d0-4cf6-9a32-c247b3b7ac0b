'use client'

// Root providers component for the application
// Wraps the app with TanStack Query, notifications, and other providers

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { useState } from 'react'
import { queryClient } from '../../lib/query-client'
import { NotificationProvider } from './NotificationProvider'
import { ModalProvider } from './ModalProvider'
import { AuthProvider } from './AuthProvider'

interface ProvidersProps {
  children: React.ReactNode
}

export function Providers({ children }: ProvidersProps) {
  // Use a state to ensure the query client is stable across re-renders
  const [client] = useState(() => queryClient)

  return (
    <QueryClientProvider client={client}>
      <AuthProvider>
        <NotificationProvider>
          <ModalProvider>
            {children}
            {process.env.NODE_ENV === 'development' && (
              <ReactQueryDevtools initialIsOpen={false} />
            )}
          </ModalProvider>
        </NotificationProvider>
      </AuthProvider>
    </QueryClientProvider>
  )
}
