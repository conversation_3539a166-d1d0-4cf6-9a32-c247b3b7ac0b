// Product filter store for managing search and filter state
// Handles category, brand, price, and attribute filters

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import type { ProductFilters } from '../types';

interface FilterState {
  // Current filters
  filters: ProductFilters;
  
  // Filter options (loaded from API)
  availableFilters: {
    categories: Array<{ id: number; name: string; slug: string }>;
    brands: Array<{ id: number; name: string; slug: string }>;
    attributes: Record<string, Array<{ id: number; name: string; value: string }>>;
    priceRange: { min: number; max: number };
  };
  
  // UI state
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setFilter: (key: keyof ProductFilters, value: any) => void;
  updateFilters: (filters: Partial<ProductFilters>) => void;
  clearFilter: (key: keyof ProductFilters) => void;
  clearAllFilters: () => void;
  resetFilters: () => void;
  
  setAvailableFilters: (filters: Partial<FilterState['availableFilters']>) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Computed getters
  hasActiveFilters: () => boolean;
  getActiveFilterCount: () => number;
  getFilterParams: () => URLSearchParams;
  getFilterSummary: () => string[];
}

const defaultFilters: ProductFilters = {
  category: undefined,
  brand: [],
  product_type: undefined,
  min_price: undefined,
  max_price: undefined,
  attributes: {},
  search: '',
  sort: '-created_at',
  page: 1,
  page_size: 20,
};

export const useFilterStore = create<FilterState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        filters: { ...defaultFilters },
        availableFilters: {
          categories: [],
          brands: [],
          attributes: {},
          priceRange: { min: 0, max: 1000 },
        },
        isLoading: false,
        error: null,
        
        // Actions
        setFilter: (key, value) => {
          set((state) => ({
            filters: {
              ...state.filters,
              [key]: value,
              page: key === 'page' ? value : 1, // Reset page when other filters change
            },
            error: null,
          }));
        },
        
        updateFilters: (newFilters) => {
          set((state) => ({
            filters: {
              ...state.filters,
              ...newFilters,
              page: newFilters.page || 1,
            },
            error: null,
          }));
        },
        
        clearFilter: (key) => {
          set((state) => {
            const newFilters = { ...state.filters };
            
            if (key === 'brand') {
              newFilters.brand = [];
            } else if (key === 'attributes') {
              newFilters.attributes = {};
            } else {
              delete newFilters[key];
            }
            
            return {
              filters: {
                ...newFilters,
                page: 1,
              },
              error: null,
            };
          });
        },
        
        clearAllFilters: () => {
          set({
            filters: { ...defaultFilters },
            error: null,
          });
        },
        
        resetFilters: () => {
          set({
            filters: { ...defaultFilters },
            error: null,
          });
        },
        
        setAvailableFilters: (filters) => {
          set((state) => ({
            availableFilters: {
              ...state.availableFilters,
              ...filters,
            },
          }));
        },
        
        setLoading: (loading) => {
          set({ isLoading: loading });
        },
        
        setError: (error) => {
          set({ error });
        },
        
        // Computed getters
        hasActiveFilters: () => {
          const { filters } = get();
          return !!(
            filters.category ||
            (filters.brand && filters.brand.length > 0) ||
            filters.product_type ||
            filters.min_price ||
            filters.max_price ||
            (filters.attributes && Object.keys(filters.attributes).length > 0) ||
            (filters.search && filters.search.trim())
          );
        },
        
        getActiveFilterCount: () => {
          const { filters } = get();
          let count = 0;
          
          if (filters.category) count++;
          if (filters.brand && filters.brand.length > 0) count += filters.brand.length;
          if (filters.product_type) count++;
          if (filters.min_price || filters.max_price) count++;
          if (filters.attributes) {
            count += Object.values(filters.attributes).reduce(
              (acc, values) => acc + (Array.isArray(values) ? values.length : 1),
              0
            );
          }
          if (filters.search && filters.search.trim()) count++;
          
          return count;
        },
        
        getFilterParams: () => {
          const { filters } = get();
          const params = new URLSearchParams();
          
          Object.entries(filters).forEach(([key, value]) => {
            if (value !== undefined && value !== null && value !== '') {
              if (Array.isArray(value)) {
                value.forEach(item => params.append(key, item.toString()));
              } else if (typeof value === 'object') {
                // Handle attributes object
                Object.entries(value).forEach(([attrKey, attrValue]) => {
                  if (Array.isArray(attrValue)) {
                    attrValue.forEach(item => params.append(`${key}[${attrKey}]`, item.toString()));
                  } else {
                    params.append(`${key}[${attrKey}]`, attrValue.toString());
                  }
                });
              } else {
                params.append(key, value.toString());
              }
            }
          });
          
          return params;
        },
        
        getFilterSummary: () => {
          const { filters, availableFilters } = get();
          const summary: string[] = [];
          
          if (filters.category) {
            const category = availableFilters.categories.find(c => c.slug === filters.category);
            if (category) summary.push(`Category: ${category.name}`);
          }
          
          if (filters.brand && filters.brand.length > 0) {
            const brandNames = filters.brand
              .map(brandSlug => availableFilters.brands.find(b => b.slug === brandSlug)?.name)
              .filter(Boolean);
            if (brandNames.length > 0) {
              summary.push(`Brands: ${brandNames.join(', ')}`);
            }
          }
          
          if (filters.min_price || filters.max_price) {
            const min = filters.min_price || availableFilters.priceRange.min;
            const max = filters.max_price || availableFilters.priceRange.max;
            summary.push(`Price: $${min} - $${max}`);
          }
          
          if (filters.search && filters.search.trim()) {
            summary.push(`Search: "${filters.search}"`);
          }
          
          return summary;
        },
      }),
      {
        name: 'filter-store',
        partialize: (state) => ({
          filters: {
            ...state.filters,
            page: 1, // Don't persist page number
          },
          // Don't persist available filters or UI state
        }),
        version: 1,
      }
    ),
    {
      name: 'FilterStore',
      enabled: process.env.NODE_ENV === 'development',
    }
  )
);

// Selector hooks for better performance
export const useFilters = () => useFilterStore((state) => state.filters);
export const useAvailableFilters = () => useFilterStore((state) => state.availableFilters);
export const useFilterLoading = () => useFilterStore((state) => state.isLoading);
export const useFilterError = () => useFilterStore((state) => state.error);
export const useHasActiveFilters = () => useFilterStore((state) => state.hasActiveFilters());
export const useActiveFilterCount = () => useFilterStore((state) => state.getActiveFilterCount());
export const useFilterSummary = () => useFilterStore((state) => state.getFilterSummary());

// Filter actions
export const useFilterActions = () => useFilterStore((state) => ({
  setFilter: state.setFilter,
  updateFilters: state.updateFilters,
  clearFilter: state.clearFilter,
  clearAllFilters: state.clearAllFilters,
  resetFilters: state.resetFilters,
  setAvailableFilters: state.setAvailableFilters,
  setLoading: state.setLoading,
  setError: state.setError,
}));

// Helper hooks
export const useFilterParams = () => useFilterStore((state) => state.getFilterParams());

export const useCurrentSort = () => useFilterStore((state) => state.filters.sort);

export const useCurrentPage = () => useFilterStore((state) => state.filters.page || 1);

export const useSearchQuery = () => useFilterStore((state) => state.filters.search || '');
