// Shopping cart store with persistence
// Manages cart items, quantities, and totals

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import type { CartItem, Product, ProductVariant } from '../types';

interface CartState {
  // State
  items: CartItem[];
  isLoading: boolean;
  error: string | null;

  // Actions
  addItem: (product: Product, variant: ProductVariant, quantity?: number) => void;
  removeItem: (itemId: string) => void;
  updateQuantity: (itemId: string, quantity: number) => void;
  clearCart: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;

  // Computed getters
  totalItems: () => number;
  totalQuantity: () => number;
  subtotal: () => number;
  isEmpty: () => boolean;
  getItem: (productId: number, variantId: number) => CartItem | undefined;
  hasItem: (productId: number, variantId: number) => boolean;
}

const calculateItemTotal = (price: string, quantity: number): string => {
  const numPrice = parseFloat(price);
  return (numPrice * quantity).toFixed(2);
};

const generateCartItemId = (productId: number, variantId: number): string => {
  return `${productId}-${variantId}`;
};

export const useCartStore = create<CartState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        items: [],
        isLoading: false,
        error: null,

        // Actions
        addItem: (product, variant, quantity = 1) => {
          const itemId = generateCartItemId(product.id, variant.id);
          const existingItem = get().items.find(item => item.id === itemId);

          if (existingItem) {
            // Update quantity of existing item
            get().updateQuantity(itemId, existingItem.quantity + quantity);
          } else {
            // Add new item
            const newItem: CartItem = {
              id: itemId,
              product,
              variant,
              quantity,
              price: variant.price,
              total: calculateItemTotal(variant.price, quantity),
            };

            set((state) => ({
              items: [...state.items, newItem],
              error: null,
            }));
          }
        },

        removeItem: (itemId) => {
          set((state) => ({
            items: state.items.filter(item => item.id !== itemId),
            error: null,
          }));
        },

        updateQuantity: (itemId, quantity) => {
          if (quantity <= 0) {
            get().removeItem(itemId);
            return;
          }

          set((state) => ({
            items: state.items.map(item =>
              item.id === itemId
                ? {
                    ...item,
                    quantity,
                    total: calculateItemTotal(item.price, quantity),
                  }
                : item
            ),
            error: null,
          }));
        },

        clearCart: () => {
          set({
            items: [],
            error: null,
          });
        },

        setLoading: (loading) => {
          set({ isLoading: loading });
        },

        setError: (error) => {
          set({ error });
        },

        clearError: () => {
          set({ error: null });
        },

        // Computed getters
        totalItems: () => get().items.length,

        totalQuantity: () => 
          get().items.reduce((total, item) => total + item.quantity, 0),

        subtotal: () =>
          get().items.reduce((total, item) => total + parseFloat(item.total), 0),

        isEmpty: () => get().items.length === 0,

        getItem: (productId, variantId) => {
          const itemId = generateCartItemId(productId, variantId);
          return get().items.find(item => item.id === itemId);
        },

        hasItem: (productId, variantId) => {
          const itemId = generateCartItemId(productId, variantId);
          return get().items.some(item => item.id === itemId);
        },
      }),
      {
        name: 'cart-store',
        partialize: (state) => ({
          items: state.items,
          // Don't persist loading states or errors
        }),
        version: 1,
      }
    ),
    {
      name: 'CartStore',
      enabled: process.env.NODE_ENV === 'development',
    }
  )
);

// Selector hooks for better performance
export const useCartItems = () => useCartStore((state) => state.items);
export const useCartLoading = () => useCartStore((state) => state.isLoading);
export const useCartError = () => useCartStore((state) => state.error);
export const useTotalItems = () => useCartStore((state) => state.totalItems());
export const useTotalQuantity = () => useCartStore((state) => state.totalQuantity());
export const useSubtotal = () => useCartStore((state) => state.subtotal());
export const useIsCartEmpty = () => useCartStore((state) => state.isEmpty());

// Cart actions
export const useCartActions = () => useCartStore((state) => ({
  addItem: state.addItem,
  removeItem: state.removeItem,
  updateQuantity: state.updateQuantity,
  clearCart: state.clearCart,
  setLoading: state.setLoading,
  setError: state.setError,
  clearError: state.clearError,
}));

// Helper hooks
export const useCartState = () => {
  const items = useCartItems();
  const isLoading = useCartLoading();
  const error = useCartError();
  const totalItems = useTotalItems();
  const totalQuantity = useTotalQuantity();
  const subtotal = useSubtotal();
  const isEmpty = useIsCartEmpty();

  return {
    items,
    isLoading,
    error,
    totalItems,
    totalQuantity,
    subtotal,
    isEmpty,
    formattedSubtotal: `$${subtotal.toFixed(2)}`,
  };
};

export const useCartItem = (productId: number, variantId: number) => {
  return useCartStore((state) => state.getItem(productId, variantId));
};

export const useHasCartItem = (productId: number, variantId: number) => {
  return useCartStore((state) => state.hasItem(productId, variantId));
};
