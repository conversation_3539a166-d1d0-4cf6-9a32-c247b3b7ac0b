/* CSS Reset - B<PERSON>er Default Styles Reset */

/* Box sizing rules */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* Remove default margin and padding */
* {
  margin: 0;
  padding: 0;
  font: inherit;
}

/* Remove list styles on ul, ol elements */
ul,
ol {
  list-style: none;
}

/* Set core root defaults */
html:focus-within {
  scroll-behavior: smooth;
}

html,
body {
  height: 100%;
}

/* Set core body defaults */
body {
  text-rendering: optimizeSpeed;
  line-height: 1.5;
}

/* Make images easier to work with */
img,
picture,
svg {
  max-width: 100%;
  display: block;
}

/* Remove default button styles */
button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  font: inherit;
  color: inherit;
  cursor: pointer;
}

/* Remove default input styles */
input,
textarea,
select {
  font: inherit;
  color: inherit;
  background: none;
  border: none;
  outline: none;
}

/* Remove default link styles */
a {
  color: inherit;
  text-decoration: none;
}

/* Remove default table styles */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* Remove default fieldset styles */
fieldset {
  border: none;
  padding: 0;
  margin: 0;
}

/* Remove default legend styles */
legend {
  padding: 0;
}

/* Inherit fonts for inputs and buttons */
input,
button,
textarea,
select {
  font: inherit;
}

/* Remove default appearance for form elements */
input,
textarea,
select,
button {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms;
    animation-iteration-count: 1;
    transition-duration: 0.01ms;
    scroll-behavior: auto;
  }
}
