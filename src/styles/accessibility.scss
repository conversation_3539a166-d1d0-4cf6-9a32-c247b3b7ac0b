// Essential Accessibility utilities
@use 'variables' as *;
@use 'mixins' as *;

// Screen reader only content
.sr-only {
  @include visually-hidden;
}

// Show content only when focused (for skip links)
.sr-only-focusable {
  @include visually-hidden;

  &:focus,
  &:active {
    position: static;
    width: auto;
    height: auto;
    padding: $spacing-2 $spacing-4;
    margin: 0;
    overflow: visible;
    clip: auto;
    white-space: normal;
    background: $primary-blue;
    color: white;
    text-decoration: none;
    border-radius: $border-radius-md;
    z-index: $z-modal;
  }
}

// Skip navigation links
.skip-links {
  position: absolute;
  top: 0;
  left: 0;
  z-index: $z-modal + 1;
  
  a {
    @extend .sr-only-focusable;
    display: block;
    margin: $spacing-2;
  }
}

// Focus management
.focus-trap {
  &:focus {
    outline: none;
  }
}

// Enhanced focus styles
.focus-visible {
  &:focus-visible {
    outline: 2px solid $primary-blue;
    outline-offset: 2px;
    border-radius: $border-radius-sm;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .button,
  .input,
  .card {
    border: 2px solid currentColor;
  }
  
  .link {
    text-decoration: underline;
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms;
    animation-iteration-count: 1;
    transition-duration: 0.01ms;
    scroll-behavior: auto;
  }
}

// Color contrast utilities
.text-high-contrast {
  color: $primary-dark-text-color !important;
}

.text-medium-contrast {
  color: $primary-text-color !important;
}

.text-low-contrast {
  color: $primary-lighter-text-color !important;
}

// Interactive element states
.interactive {
  cursor: pointer;
  transition: all $transition-fast;

  &:hover {
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }

  &:focus-visible {
    @extend .focus-visible;
  }

  &:disabled,
  &[aria-disabled="true"] {
    cursor: not-allowed;
    opacity: 0.6;
    transform: none !important;
  }
}

// ARIA live regions
.live-region {
  position: absolute;
  left: -10000px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

// Loading states
.loading {
  position: relative;
  pointer-events: none;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
  }
}

// Error states
.error-state {
  border-color: $error !important;
  
  &:focus {
    box-shadow: 0 0 0 3px rgba($error, 0.2) !important;
  }
}

// Success states
.success-state {
  border-color: $success !important;
  
  &:focus {
    box-shadow: 0 0 0 3px rgba($success, 0.2) !important;
  }
}

// Keyboard navigation
.keyboard-nav {
  .focusable:focus {
    outline: 2px solid $primary-blue;
    outline-offset: 2px;
  }
}

// Touch targets (minimum 44px)
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

// Text scaling support
@media (min-resolution: 2dppx) {
  .text-scalable {
    font-size: calc(1rem + 0.5vw);
  }
}

// Dark mode accessibility
@media (prefers-color-scheme: dark) {
  .text-high-contrast {
    color: #F9FAFB !important;
  }

  .text-medium-contrast {
    color: #E5E7EB !important;
  }

  .text-low-contrast {
    color: #D1D5DB !important;
  }

  .focus-visible:focus-visible {
    outline-color: #60A5FA;
  }

  .loading::after {
    background: rgba(31, 41, 55, 0.8);
  }
}

// Print styles
@media print {
  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }

  // Ensure good contrast for printing
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }

  a {
    text-decoration: underline !important;
  }

  // Show URLs for links
  a[href]:after {
    content: " (" attr(href) ")";
    font-size: 0.8em;
    color: #666;
  }
}

// Form accessibility
.form-field {
  position: relative;

  .required::after {
    content: " *";
    color: $error;
    font-weight: bold;
  }

  .error-message {
    color: $error;
    font-size: $font-size-1;
    margin-top: $spacing-1;
    display: flex;
    align-items: center;
    gap: $spacing-1;
  }

  .help-text {
    color: $primary-lighter-text-color;
    font-size: $font-size-1;
    margin-top: $spacing-1;
  }
}

// Modal accessibility
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: $z-modal;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-4;
}

.modal-content {
  background: white;
  border-radius: $border-radius-lg;
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  position: relative;

  &:focus {
    outline: none;
  }
}

// Tooltip accessibility
.tooltip {
  position: relative;

  &[aria-describedby] {
    cursor: help;
  }
}

.tooltip-content {
  position: absolute;
  z-index: $z-tooltip;
  background: $primary-dark-text-color;
  color: white;
  padding: $spacing-2 $spacing-3;
  border-radius: $border-radius-md;
  font-size: $font-size-1;
  white-space: nowrap;
  pointer-events: none;
  opacity: 0;
  transition: opacity $transition-fast;

  &.visible {
    opacity: 1;
  }
}

// Table accessibility
.accessible-table {
  caption {
    font-weight: bold;
    text-align: left;
    margin-bottom: $spacing-2;
  }

  th {
    text-align: left;
    font-weight: bold;
    background: $gray-100;
  }

  th,
  td {
    padding: $spacing-3;
    border: 1px solid $gray-300;
  }

  tbody tr:nth-child(even) {
    background: $gray-50;
  }

  tbody tr:hover {
    background: $sky-lighter-blue;
  }
}

// Responsive text
.responsive-text {
  font-size: clamp(0.875rem, 2.5vw, 1.125rem);
  line-height: 1.6;
}

// Color blind friendly indicators
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: $spacing-1;

  &::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
  }

  &.success::before {
    background: $success;
  }

  &.warning::before {
    background: $warning;
  }

  &.error::before {
    background: $error;
  }

  &.info::before {
    background: $info;
  }
}
