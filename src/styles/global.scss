// Global styles for the entire project
@use './variables' as *;
@use './mixins' as *;

// Global typography and base styles
body {
  font-family: $primary-font-family;
  color: $primary-dark-text-color;
  background-color: white;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// Focus styles for accessibility
:focus-visible {
  outline: 2px solid $primary-blue;
  outline-offset: 2px;
}

// Common utility classes
.container {
  @include container;
}

.sr-only {
  @include visually-hidden;
}

.text-truncate {
  @include text-truncate;
}

// Button base styles
.btn {
  @include btn($primary-dark-text-color, white, $gray-300);
}

.btn-primary {
  @include btn(white, $primary-blue, $primary-blue);
  
  &:hover {
    background-color: $primary-blue-dark;
    border-color: $primary-blue-dark;
  }
}

.btn-secondary {
  @include btn($primary-blue, transparent, $primary-blue);
  
  &:hover {
    background-color: $primary-blue;
    color: white;
  }
}

// Loading animations
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// Utility animations
.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

// Scrollbar styling
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: $gray-100;
}

::-webkit-scrollbar-thumb {
  background: $gray-400;
  border-radius: $border-radius-full;
}

::-webkit-scrollbar-thumb:hover {
  background: $gray-500;
}

// Print styles
@media print {
  .no-print {
    display: none;
  }
  
  body {
    background: white;
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms;
    animation-iteration-count: 1;
    transition-duration: 0.01ms;
  }
}
