@use './variables' as vars;

// Essential Mixins

// Flexbox mixin
@mixin flexbox($justify: center, $align: center, $direction: row, $wrap: nowrap) {
  display: flex;
  justify-content: $justify;
  align-items: $align;
  flex-direction: $direction;
  flex-wrap: $wrap;
}

// Button mixin
@mixin btn($color, $bg-color, $border-color: transparent) {
  @include flexbox(center, center);
  color: $color;
  background-color: $bg-color;
  border: 1px solid $border-color;
  padding: vars.$spacing-3 vars.$spacing-4;
  border-radius: vars.$border-radius-md;
  font-weight: 500;
  cursor: pointer;
  transition: all vars.$transition-fast;
  text-decoration: none;
  display: inline-flex;

  &:hover {
    opacity: 0.9;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

// Container mixin
@mixin container($max-width: 1200px) {
  width: 100%;
  max-width: $max-width;
  margin: 0 auto;
  padding: 0 vars.$spacing-4;
}

// Media query mixins
@mixin mobile-only {
  @media (max-width: #{vars.$mobile - 1px}) {
    @content;
  }
}

@mixin tablet-up {
  @media (min-width: vars.$tablet) {
    @content;
  }
}

@mixin laptop-up {
  @media (min-width: vars.$laptop) {
    @content;
  }
}

@mixin desktop-up {
  @media (min-width: vars.$desktop) {
    @content;
  }
}

// Text utilities
@mixin text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// Visually hidden (for screen readers)
@mixin visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// Card mixin
@mixin card {
  background: white;
  border-radius: vars.$border-radius-lg;
  box-shadow: vars.$box-shadow-sm;
  border: 1px solid vars.$gray-200;
  overflow: hidden;

  &:hover {
    box-shadow: vars.$box-shadow-md;
  }
}

// Input base mixin
@mixin input-base {
  width: 100%;
  padding: vars.$spacing-3 vars.$spacing-4;
  border: 1px solid vars.$gray-300;
  border-radius: vars.$border-radius-md;
  font-size: vars.$font-size-2;
  transition: all vars.$transition-fast;

  &:focus {
    outline: none;
    border-color: vars.$primary-blue;
    box-shadow: 0 0 0 2px rgba(0, 145, 207, 0.2);
  }

  &:disabled {
    background-color: vars.$gray-100;
    cursor: not-allowed;
  }
}

// Text clamp mixin
@mixin text-clamp($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}